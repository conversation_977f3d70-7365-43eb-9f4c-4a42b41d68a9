/**
 * 生成线性趋势的随机数据点
 * @param {number} count - 数据点数量
 * @param {number} slope - 斜率
 * @param {number} intercept - 截距
 * @param {number} noise - 噪声强度
 * @param {number} xMin - X最小值
 * @param {number} xMax - X最大值
 * @returns {Array} 数据点数组 [{x, y}, ...]
 */
export function generateLinearData(count = 50, slope = 1, intercept = 0, noise = 0.5, xMin = 0, xMax = 100) {
    const data = [];
    const xRange = xMax - xMin;

    for (let i = 0; i < count; i++) {
        const x = xMin + (i / (count - 1)) * xRange;
        const baseY = slope * x + intercept;
        const y = baseY + (Math.random() - 0.5) * 2 * noise * baseY;
        data.push({ x, y });
    }

    return data;
}

/**
 * 生成正弦波数据点
 * @param {number} count - 数据点数量
 * @param {number} amplitude - 振幅
 * @param {number} frequency - 频率
 * @param {number} phase - 相位
 * @param {number} noise - 噪声强度
 * @param {number} xMin - X最小值
 * @param {number} xMax - X最大值
 * @returns {Array} 数据点数组 [{x, y}, ...]
 */
export function generateSineData(count = 50, amplitude = 1, frequency = 1, phase = 0, noise = 0.1, xMin = 0, xMax = 2 * Math.PI) {
    const data = [];
    const xRange = xMax - xMin;

    for (let i = 0; i < count; i++) {
        const x = xMin + (i / (count - 1)) * xRange;
        const baseY = amplitude * Math.sin(frequency * x + phase);
        const y = baseY + (Math.random() - 0.5) * 2 * noise * amplitude;
        data.push({ x, y });
    }

    return data;
}

/**
 * 生成指数趋势数据点
 * @param {number} count - 数据点数量
 * @param {number} base - 指数底数
 * @param {number} coefficient - 系数
 * @param {number} noise - 噪声强度
 * @param {number} xMin - X最小值
 * @param {number} xMax - X最大值
 * @returns {Array} 数据点数组 [{x, y}, ...]
 */
export function generateExponentialData(count = 50, base = 2, coefficient = 1, noise = 0.3, xMin = 0, xMax = 5) {
    const data = [];
    const xRange = xMax - xMin;

    for (let i = 0; i < count; i++) {
        const x = xMin + (i / (count - 1)) * xRange;
        const baseY = coefficient * Math.pow(base, x);
        const y = baseY + (Math.random() - 0.5) * 2 * noise * baseY;
        data.push({ x, y });
    }

    return data;
}

/**
 * 生成随机分布的数据点
 * @param {number} count - 数据点数量
 * @param {number} xMin - X最小值
 * @param {number} xMax - X最大值
 * @param {number} yMin - Y最小值
 * @param {number} yMax - Y最大值
 * @returns {Array} 数据点数组 [{x, y}, ...]
 */
export function generateRandomData(count = 50, xMin = 0, xMax = 100, yMin = 0, yMax = 100) {
    const data = [];
    const xRange = xMax - xMin;
    const yRange = yMax - yMin;

    for (let i = 0; i < count; i++) {
        const x = xMin + Math.random() * xRange;
        const y = yMin + Math.random() * yRange;
        data.push({ x, y });
    }

    return data;
}

/**
 * 生成带聚类效应的随机数据点
 * @param {number} count - 数据点数量
 * @param {number} clusters - 聚类数量
 * @param {number} xMin - X最小值
 * @param {number} xMax - X最大值
 * @param {number} yMin - Y最小值
 * @param {number} yMax - Y最大值
 * @param {number} clusterSpread - 聚类扩散程度
 * @returns {Array} 数据点数组 [{x, y}, ...]
 */
export function generateClusteredData(count = 50, clusters = 3, xMin = 0, xMax = 100, yMin = 0, yMax = 100, clusterSpread = 0.1) {
    const data = [];
    const xRange = xMax - xMin;
    const yRange = yMax - yMin;

    // 生成聚类中心
    const centers = [];
    for (let i = 0; i < clusters; i++) {
        centers.push({
            x: xMin + Math.random() * xRange,
            y: yMin + Math.random() * yRange
        });
    }

    // 生成围绕聚类中心的数据点
    for (let i = 0; i < count; i++) {
        const center = centers[Math.floor(Math.random() * centers.length)];
        const x = center.x + (Math.random() - 0.5) * 2 * clusterSpread * xRange;
        const y = center.y + (Math.random() - 0.5) * 2 * clusterSpread * yRange;
        data.push({ x, y });
    }

    return data;
}

/**
 * 生成时间序列数据点
 * @param {number} count - 数据点数量
 * @param {number} trend - 趋势强度
 * @param {number} seasonality - 季节性强度
 * @param {number} noise - 噪声强度
 * @param {Date} startDate - 开始日期
 * @param {number} interval - 时间间隔（毫秒）
 * @returns {Array} 数据点数组 [{x: Date, y}, ...]
 */
export function generateTimeSeriesData(count = 50, trend = 0.1, seasonality = 0.5, noise = 0.2, startDate = new Date(), interval = 24 * 60 * 60 * 1000) {
    const data = [];
    let currentDate = new Date(startDate);

    for (let i = 0; i < count; i++) {
        const timeValue = i / count;
        const seasonal = seasonality * Math.sin(2 * Math.PI * timeValue * 4); // 4个周期
        const trendValue = trend * i;
        const noiseValue = (Math.random() - 0.5) * 2 * noise;

        const y = 10 + trendValue + seasonal + noiseValue;

        data.push({
            x: new Date(currentDate),
            y
        });

        currentDate = new Date(currentDate.getTime() + interval);
    }

    return data;
}

/**
 * 生成对数趋势数据点
 * @param {number} count - 数据点数量
 * @param {number} coefficient - 系数
 * @param {number} noise - 噪声强度
 * @param {number} xMin - X最小值
 * @param {number} xMax - X最大值
 * @returns {Array} 数据点数组 [{x, y}, ...]
 */
export function generateLogarithmicData(count = 50, coefficient = 10, noise = 0.2, xMin = 1, xMax = 100) {
    const data = [];
    const xRange = xMax - xMin;

    for (let i = 0; i < count; i++) {
        const x = xMin + (i / (count - 1)) * xRange;
        const baseY = coefficient * Math.log(x);
        const y = baseY + (Math.random() - 0.5) * 2 * noise * baseY;
        data.push({ x, y });
    }

    return data;
}

/**
 * 生成多项式数据点
 * @param {number} count - 数据点数量
 * @param {Array} coefficients - 多项式系数 [a, b, c, ...] 对应 ax² + bx + c
 * @param {number} noise - 噪声强度
 * @param {number} xMin - X最小值
 * @param {number} xMax - X最大值
 * @returns {Array} 数据点数组 [{x, y}, ...]
 */
export function generatePolynomialData(count = 50, coefficients = [1, -2, 1], noise = 0.1, xMin = -5, xMax = 5) {
    const data = [];
    const xRange = xMax - xMin;

    for (let i = 0; i < count; i++) {
        const x = xMin + (i / (count - 1)) * xRange;
        let baseY = 0;

        // 计算多项式值
        for (let j = 0; j < coefficients.length; j++) {
            baseY += coefficients[j] * Math.pow(x, coefficients.length - 1 - j);
        }

        const y = baseY + (Math.random() - 0.5) * 2 * noise * Math.abs(baseY);
        data.push({ x, y });
    }

    return data;
}
