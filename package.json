{"name": "d3charts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@vueuse/core": "^13.9.0", "d3": "^7.9.0", "gsap": "^3.13.0", "vue": "^3.5.18"}, "devDependencies": {"@types/d3": "^7.4.3", "@vitejs/plugin-vue": "^6.0.1", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.1.2", "vue-tsc": "^3.0.5"}}