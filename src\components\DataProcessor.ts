/**
 * DataProcessor: 纯数据计算 + Worker 封装
 * - 负责插值、（可扩展）抽稀、指标等
 */
export type SimplePoint = { x: number; y: number; currentY?: number };

export interface InterpolatePayload {
  id: number;
  type: 'interpolateAtX';
  data: { points: SimplePoint[]; targetX: number };
}

export class DataProcessor {
  private data: SimplePoint[] = [];
  private useWorker = false;
  private worker: Worker | null = null;
  private reqId = 0;
  private pending: { [key: number]: ((result: number) => void) | undefined } = {};

  constructor(useWorker: boolean = false) {
    this.useWorker = useWorker;
    this.syncWorker();
  }

  setUseWorker(v: boolean) {
    this.useWorker = v;
    this.syncWorker();
  }

  setData(points: SimplePoint[]) {
    this.data = points;
  }

  dispose() {
    this.terminateWorker();
    this.pending = {};
  }

  private syncWorker() {
    if (this.useWorker) this.initWorker();
    else this.terminateWorker();
  }

  private initWorker() {
    if (this.worker) return;
    try {
      // @ts-ignore
      this.worker = new Worker(new URL('../workers/chartWorker.ts', import.meta.url), { type: 'module' });
      this.worker.onmessage = (ev: MessageEvent) => {
        const msg: any = ev.data;
        if (!msg || typeof msg.id !== 'number') return;
        const resolver = this.pending[msg.id];
        if (resolver) {
          delete this.pending[msg.id];
          resolver(msg.result as number);
        }
      };
    } catch (e) {
      console.warn('Worker 初始化失败，使用主线程计算。', e);
      this.worker = null;
    }
  }

  private terminateWorker() {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
  }

  private interpolateAtXMainThread(x: number): number {
    const arr = this.data;
    if (arr.length === 0) return 0;
    if (x <= arr[0].x) return arr[0].currentY ?? arr[0].y;
    if (x >= arr[arr.length - 1].x) {
      const last = arr[arr.length - 1];
      return last.currentY ?? last.y;
    }
    for (let i = 0; i < arr.length - 1; i++) {
      const a = arr[i], b = arr[i + 1];
      if (a.x <= x && x <= b.x) {
        const ay = a.currentY ?? a.y;
        const by = b.currentY ?? b.y;
        const t = (x - a.x) / (b.x - a.x || 1);
        return ay + (by - ay) * t;
      }
    }
    const last = arr[arr.length - 1];
    return last.currentY ?? last.y;
  }

  interpolateAtX(x: number, timeoutMs: number = 200): Promise<number> {
    if (!this.worker) {
      return Promise.resolve(this.interpolateAtXMainThread(x));
    }
    const id = ++this.reqId;
    const payload: InterpolatePayload = {
      id,
      type: 'interpolateAtX',
      data: {
        points: this.data.map((d) => ({ x: d.x, y: d.y, currentY: d.currentY })),
        targetX: x
      }
    };
    this.worker.postMessage(payload);
    return new Promise<number>((resolve) => {
      this.pending[id] = resolve;
      setTimeout(() => {
        const resolver = this.pending[id];
        if (resolver) {
          delete this.pending[id];
          resolve(this.interpolateAtXMainThread(x));
        }
      }, timeoutMs);
    });
  }
}