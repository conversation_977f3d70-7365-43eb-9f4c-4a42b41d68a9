/**
 * DataProcessor: 纯数据计算 + Worker 封装
 * - 负责插值、缩放计算、路径生成、数据处理等
 */
export type SimplePoint = { x: number; y: number; currentY?: number };

export type WorkerPayload =
  | InterpolatePayload
  | ScaleCalculationPayload
  | PathGenerationPayload
  | DataProcessingPayload;

export interface InterpolatePayload {
  id: number;
  type: 'interpolateAtX';
  data: { points: SimplePoint[]; targetX: number };
}

export interface ScaleCalculationPayload {
  id: number;
  type: 'calculateScales';
  data: {
    points: SimplePoint[];
    width: number;
    height: number;
    margin: { top: number; right: number; bottom: number; left: number };
    zoomLevel: number;
    viewOffsetX: number;
    viewOffsetY: number;
  };
}

export interface PathGenerationPayload {
  id: number;
  type: 'generatePath';
  data: {
    points: SimplePoint[];
    curveType: 'linear' | 'cardinal' | 'basis' | 'monotone';
    progress: number;
  };
}

export interface DataProcessingPayload {
  id: number;
  type: 'processData';
  data: {
    points: SimplePoint[];
    operations: ('smooth' | 'downsample' | 'normalize')[];
  };
}

export class DataProcessor {
  private data: SimplePoint[] = [];
  private useWorker = false;
  private worker: Worker | null = null;
  private reqId = 0;
  private pending: { [key: number]: ((result: any) => void) | undefined } = {};

  constructor(useWorker: boolean = false) {
    this.useWorker = useWorker;
    this.syncWorker();
  }

  setUseWorker(v: boolean) {
    this.useWorker = v;
    this.syncWorker();
  }

  setData(points: SimplePoint[]) {
    this.data = points;
  }

  dispose() {
    this.terminateWorker();
    this.pending = {};
  }

  private syncWorker() {
    if (this.useWorker) this.initWorker();
    else this.terminateWorker();
  }

  private initWorker() {
    if (this.worker) return;
    try {
      // @ts-ignore
      this.worker = new Worker(new URL('../workers/chartWorker.ts', import.meta.url), { type: 'module' });
      this.worker.onmessage = (ev: MessageEvent) => {
        const msg: any = ev.data;
        if (!msg || typeof msg.id !== 'number') return;
        const resolver = this.pending[msg.id];
        if (resolver) {
          delete this.pending[msg.id];
          resolver(msg.result);
        }
      };
    } catch (e) {
      console.warn('Worker 初始化失败，使用主线程计算。', e);
      this.worker = null;
    }
  }

  private terminateWorker() {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
  }

  private interpolateAtXMainThread(x: number): number {
    const arr = this.data;
    if (arr.length === 0) return 0;
    if (x <= arr[0].x) return arr[0].currentY ?? arr[0].y;
    if (x >= arr[arr.length - 1].x) {
      const last = arr[arr.length - 1];
      return last.currentY ?? last.y;
    }
    for (let i = 0; i < arr.length - 1; i++) {
      const a = arr[i], b = arr[i + 1];
      if (a.x <= x && x <= b.x) {
        const ay = a.currentY ?? a.y;
        const by = b.currentY ?? b.y;
        const t = (x - a.x) / (b.x - a.x || 1);
        return ay + (by - ay) * t;
      }
    }
    const last = arr[arr.length - 1];
    return last.currentY ?? last.y;
  }

  interpolateAtX(x: number, timeoutMs: number = 200): Promise<number> {
    if (!this.worker) {
      return Promise.resolve(this.interpolateAtXMainThread(x));
    }
    return this.sendWorkerMessage({
      id: ++this.reqId,
      type: 'interpolateAtX',
      data: {
        points: this.data.map((d) => ({ x: d.x, y: d.y, currentY: d.currentY })),
        targetX: x
      }
    }, timeoutMs, () => this.interpolateAtXMainThread(x));
  }

  // 缩放计算
  calculateScales(
    width: number,
    height: number,
    margin: { top: number; right: number; bottom: number; left: number },
    zoomLevel: number,
    viewOffsetX: number,
    viewOffsetY: number,
    timeoutMs: number = 200
  ): Promise<any> {
    if (!this.worker) {
      // 主线程回退逻辑
      return Promise.resolve(this.calculateScalesMainThread(width, height, margin, zoomLevel, viewOffsetX, viewOffsetY));
    }
    return this.sendWorkerMessage({
      id: ++this.reqId,
      type: 'calculateScales',
      data: {
        points: this.data.map((d) => ({ x: d.x, y: d.y, currentY: d.currentY })),
        width,
        height,
        margin,
        zoomLevel,
        viewOffsetX,
        viewOffsetY
      }
    }, timeoutMs, () => this.calculateScalesMainThread(width, height, margin, zoomLevel, viewOffsetX, viewOffsetY));
  }

  // 路径生成
  generatePath(
    curveType: 'linear' | 'cardinal' | 'basis' | 'monotone',
    progress: number,
    timeoutMs: number = 200
  ): Promise<SimplePoint[]> {
    if (!this.worker) {
      return Promise.resolve(this.generatePathMainThread(curveType, progress));
    }
    return this.sendWorkerMessage({
      id: ++this.reqId,
      type: 'generatePath',
      data: {
        points: this.data.map((d) => ({ x: d.x, y: d.y, currentY: d.currentY })),
        curveType,
        progress
      }
    }, timeoutMs, () => this.generatePathMainThread(curveType, progress));
  }

  // 数据处理
  processData(
    operations: ('smooth' | 'downsample' | 'normalize')[],
    timeoutMs: number = 200
  ): Promise<SimplePoint[]> {
    if (!this.worker) {
      return Promise.resolve(this.processDataMainThread(operations));
    }
    return this.sendWorkerMessage({
      id: ++this.reqId,
      type: 'processData',
      data: {
        points: this.data.map((d) => ({ x: d.x, y: d.y, currentY: d.currentY })),
        operations
      }
    }, timeoutMs, () => this.processDataMainThread(operations));
  }

  // 通用Worker消息发送方法
  private sendWorkerMessage<T>(payload: WorkerPayload, timeoutMs: number, fallback: () => T): Promise<T> {
    if (!this.worker) {
      return Promise.resolve(fallback());
    }

    this.worker.postMessage(payload);
    return new Promise<T>((resolve) => {
      this.pending[payload.id] = resolve;
      setTimeout(() => {
        const resolver = this.pending[payload.id];
        if (resolver) {
          delete this.pending[payload.id];
          resolve(fallback());
        }
      }, timeoutMs);
    });
  }

  // 主线程回退方法
  private calculateScalesMainThread(
    width: number,
    height: number,
    margin: { top: number; right: number; bottom: number; left: number },
    zoomLevel: number,
    viewOffsetX: number,
    viewOffsetY: number
  ) {
    const points = this.data;
    const xMax = Math.max(...points.map(p => p.x));
    const yMax = Math.max(...points.map(p => p.y));

    if (Math.abs(zoomLevel - 1) < 0.001) {
      return {
        xDomain: [0, xMax],
        yDomain: [0, yMax],
        xRange: [margin.left, margin.left + width],
        yRange: [margin.top + height, margin.top]
      };
    }

    const zoomFactor = 1 / zoomLevel;
    const visibleXRange = xMax * zoomFactor;
    const visibleYRange = yMax * zoomFactor;

    const offsetXData = (viewOffsetX / width) * visibleXRange;
    const offsetYData = -(viewOffsetY / height) * visibleYRange;

    const centerX = xMax / 2 + offsetXData;
    const centerY = yMax / 2 + offsetYData;

    const halfVisibleX = visibleXRange / 2;
    const halfVisibleY = visibleYRange / 2;

    let xMin = centerX - halfVisibleX;
    let xMaxVisible = centerX + halfVisibleX;
    let yMin = centerY - halfVisibleY;
    let yMaxVisible = centerY + halfVisibleY;

    // 边界检查
    if (xMin < 0) {
      xMaxVisible += -xMin;
      xMin = 0;
    }
    if (xMaxVisible > xMax) {
      xMin -= (xMaxVisible - xMax);
      xMaxVisible = xMax;
      xMin = Math.max(0, xMin);
    }

    if (yMin < 0) {
      yMaxVisible += -yMin;
      yMin = 0;
    }
    if (yMaxVisible > yMax) {
      yMin -= (yMaxVisible - yMax);
      yMaxVisible = yMax;
      yMin = Math.max(0, yMin);
    }

    return {
      xDomain: [xMin, xMaxVisible],
      yDomain: [yMin, yMaxVisible],
      xRange: [margin.left, margin.left + width],
      yRange: [margin.top + height, margin.top]
    };
  }

  private generatePathMainThread(curveType: string, progress: number): SimplePoint[] {
    const points = this.data;
    if (points.length < 2) return [];

    // 注意：这里是简化版本，实际的曲线类型处理在渲染层
    // curveType 参数保留用于未来扩展
    console.log(`生成路径，曲线类型: ${curveType}, 进度: ${progress}`);

    const n = points.length;
    const p = Math.max(0, Math.min(1, progress));
    const t = p * (n - 1);
    const k = Math.floor(t);
    const f = t - k;

    const result = points.slice(0, k + 1);

    if (k < n - 1) {
      const a = points[k];
      const b = points[k + 1];
      const ax = a.x, bx = b.x;
      const ay = a.currentY ?? a.y;
      const by = b.currentY ?? b.y;
      const ix = ax + (bx - ax) * f;
      const iy = ay + (by - ay) * f;
      result.push({ x: ix, y: iy, currentY: iy });
    }

    return result;
  }

  private processDataMainThread(operations: ('smooth' | 'downsample' | 'normalize')[]): SimplePoint[] {
    let result = [...this.data];

    for (const op of operations) {
      switch (op) {
        case 'smooth':
          if (result.length > 2) {
            const smoothed = [result[0]];
            for (let i = 1; i < result.length - 1; i++) {
              const prev = result[i - 1];
              const curr = result[i];
              const next = result[i + 1];
              smoothed.push({
                x: curr.x,
                y: (prev.y + curr.y + next.y) / 3,
                currentY: curr.currentY
              });
            }
            smoothed.push(result[result.length - 1]);
            result = smoothed;
          }
          break;
        case 'downsample':
          if (result.length > 100) {
            const step = Math.ceil(result.length / 100);
            result = result.filter((_, i) => i % step === 0);
          }
          break;
        case 'normalize':
          const maxY = Math.max(...result.map(p => p.y));
          if (maxY > 0) {
            result = result.map(p => ({
              ...p,
              y: p.y / maxY
            }));
          }
          break;
      }
    }

    return result;
  }
}