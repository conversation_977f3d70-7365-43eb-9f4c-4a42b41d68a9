import { ref, onMounted, onUnmounted, type Ref } from "vue";

/**
 * 获取鼠标在指定DOM元素（如Canvas）内的坐标
 * @param targetRef - 一个指向目标DOM元素的Vue Ref
 * @returns {x: Ref<number>} 鼠标在元素内的X坐标
 * @returns {y: Ref<number>} 鼠标在元素内的Y坐标
 */
export function useMouseInCanvas(targetRef: Ref<HTMLElement | null>) {
    const x = ref(0);
    const y = ref(0);

    function update(event: MouseEvent) {
        // 确保目标元素存在
        if (targetRef.value) {
            // getBoundingClientRect() 返回元素的大小及其相对于视口的位置
            const rect = targetRef.value.getBoundingClientRect();

            // event.clientX 是鼠标相对于视口的X坐标
            // rect.left 是元素左边框相对于视口的X坐标
            // 相减即可得到鼠标在元素内部的X坐标
            x.value = event.clientX - rect.left;
            y.value = event.clientY - rect.top;
        }
    }

    // 在组件挂载后，给目标元素添加监听器
    onMounted(() => {
        // 使用 ?. 可选链操作符确保 targetRef.value 存在
        targetRef.value?.addEventListener('mousemove', update);
    });

    // 在组件卸载前，移除监听器，防止内存泄漏
    onUnmounted(() => {
        targetRef.value?.removeEventListener('mousemove', update);
    });

    return { x, y };
}