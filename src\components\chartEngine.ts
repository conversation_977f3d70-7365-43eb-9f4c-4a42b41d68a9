// chartEngine.ts
import * as d3 from "d3";
import { gsap } from "gsap";
import { ChartRenderer } from "./ChartRenderer";
import { DataProcessor } from "./DataProcessor";




/** -------------------- 配置接口 -------------------- */
interface Margin {
    top: number;
    right: number;
    bottom: number;
    left: number;
}

interface Bounds {
    min: number;
    max: number;
}

// 基础配置接口
interface BaseChartOptions {
    margin?: Margin;
    color?: string;
    duration?: number;
    showGrid?: boolean;
    showAxisLabels?: boolean;
    axisLabelColor?: string;
    axisLabelFont?: string;
}

// 折线图特定配置
interface LineChartOptions extends BaseChartOptions {
    strokeWidth?: number;
    showPoints?: boolean;
    pointRadius?: number;
    smooth?: boolean;
    curveType?: 'linear' | 'cardinal' | 'basis' | 'monotone';
    pathAnimation?: boolean;
    pathAnimationDuration?: number;
}

// 交互配置
interface InteractionOptions {
    cursorBounds?: Bounds;
    enableDrag?: boolean;
    hoverEffects?: boolean;
    cursorFollowCurve?: boolean; // 游标是否跟随曲线高度
    useWorker?: boolean; // 是否启用 Web Worker 加速
    zoomLevel?: number; // 缩放级别
    enableZoom?: boolean; // 是否启用缩放
    viewOffsetX?: number; // 视图X偏移
    viewOffsetY?: number; // 视图Y偏移
}

// 完整配置接口
export type ChartOptions = LineChartOptions & InteractionOptions;

/** -------------------- 数据接口 -------------------- */
export interface ChartData {
    x: number;
    y: number;
    currentY?: number; // 用于动画的当前值
}

/** -------------------- 交互元素基类 -------------------- */
export abstract class ChartElement {
    public id: string;
    public x: number;
    public y: number;

    constructor(id: string, x: number, y: number) {
        this.id = id;
        this.x = x;
        this.y = y;
    }

    abstract draw(ctx: CanvasRenderingContext2D): void;
    abstract isPointInside(x: number, y: number): boolean;
}

/** 游标类 */
export class Cursor extends ChartElement {
    public startY: number;
    public endY: number;
    public color: string;
    public width: number;

    constructor(
        id: string, 
        x: number, 
        startY: number, 
        endY: number,
        color: string = '#ff6b6b',
        width: number = 2
    ) {
        super(id, x, startY);
        this.startY = startY;
        this.endY = endY;
        this.color = color;
        this.width = width;
    }

    draw(ctx: CanvasRenderingContext2D): void {
        ctx.beginPath();
        ctx.moveTo(this.x, this.startY);
        ctx.lineTo(this.x, this.endY);
        ctx.strokeStyle = this.color;
        ctx.lineWidth = this.width;
        ctx.stroke();

        // 在游标线底部添加正三角形箭头（指向下方）
        ctx.beginPath();
        ctx.moveTo(this.x, this.endY); // 顶点在游标线端点
        ctx.lineTo(this.x - 3, this.endY + 6); // 左下
        ctx.lineTo(this.x + 3, this.endY + 6); // 右下
        ctx.closePath();
        ctx.fillStyle = this.color;
        ctx.fill();
    }

    isPointInside(x: number, y: number): boolean {
        return Math.abs(x - this.x) <= 5 && y >= this.startY && y <= this.endY;
    }

    getLength(): number {
        return Math.abs(this.endY - this.startY);
    }
}

/** 高亮点类 */
export class HighlightPoint extends ChartElement {
    public radius: number;
    public color: string;

    constructor(
        id: string, 
        x: number, 
        y: number, 
        radius: number = 4,
        color: string = '#ffd93d'
    ) {
        super(id, x, y);
        this.radius = radius;
        this.color = color;
    }

    draw(ctx: CanvasRenderingContext2D): void {
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.radius, 0, 2 * Math.PI);
        ctx.fillStyle = this.color;
        ctx.fill();
    }

    isPointInside(x: number, y: number): boolean {
        const dx = x - this.x;
        const dy = y - this.y;
        return Math.sqrt(dx * dx + dy * dy) <= this.radius;
    }
}

/** -------------------- 事件系统 -------------------- */
export type ChartEvent = 'cursorMove' | 'dataAnimationComplete';
export type EventCallback = (payload?: any) => void;

/** -------------------- 主图表类 -------------------- */
export class Chart {


    /** 计算给定 dataX 对应的 currentY（线性插值），用于游标贴合曲线 */
    private getInterpolatedCurrentYAtDataX(dataX: number): number {
        const n = this.data.length;
        if (n === 0) return 0;
        const first = this.data[0];
        const last = this.data[n - 1];
        if (dataX <= first.x) return first.currentY ?? first.y;
        if (dataX >= last.x) return last.currentY ?? last.y;

        // 二分查找定位 dataX 所在的相邻区间 [lo, hi]
        let lo = 0, hi = n - 1;
        while (lo + 1 < hi) {
            const mid = (lo + hi) >> 1;
            const mx = this.data[mid].x;
            if (mx === dataX) {
                const my = this.data[mid].currentY ?? this.data[mid].y;
                return my;
            }
            if (mx < dataX) {
                lo = mid;
            } else {
                hi = mid;
            }
        }

        const a = this.data[lo];
        const b = this.data[hi];
        const ay = a.currentY ?? a.y;
        const by = b.currentY ?? b.y;
        const t = (dataX - a.x) / (b.x - a.x || 1);
        return ay + (by - ay) * t;
    }


    private options: Required<ChartOptions>;
    private xScale!: d3.ScaleLinear<number, number>;
    private yScale!: d3.ScaleLinear<number, number>;
    private elements: { [key: string]: ChartElement } = {};
    private events: { [key: string]: EventCallback[] } = {};
    private pathProgress = 0; // 路径绘制进度 (0-1)
    private canvasBg: HTMLCanvasElement;
    private canvasFg: HTMLCanvasElement;
    private type: 'line' | 'bar';
    private data: ChartData[];

    // 新增模块
    private renderer!: ChartRenderer;
    private dataProcessor!: DataProcessor;



    // 默认配置
    static readonly DEFAULT_OPTIONS: Required<ChartOptions> = {
        margin: { top: 20, right: 30, bottom: 40, left: 50 },
        color: '#4285f4',
        duration: 1.5,
        strokeWidth: 2,
        showPoints: false,
        pointRadius: 4,
        showGrid: true,
        showAxisLabels: true,
        axisLabelColor: '#666',
        axisLabelFont: '12px Arial',
        smooth: false,
        curveType: 'cardinal',
        pathAnimation: true,
        pathAnimationDuration: 2,
        cursorBounds: { min: 50, max: 750 },
        enableDrag: true,
        hoverEffects: true,
        cursorFollowCurve: false,
        useWorker: false,
        zoomLevel: 1,
        enableZoom: false,
        viewOffsetX: 0,
        viewOffsetY: 0
    };

    constructor(
        canvasBg: HTMLCanvasElement,
        canvasFg: HTMLCanvasElement,
        type: 'line' | 'bar',
        data: ChartData[],
        userOptions: Partial<ChartOptions> = {}
    ) {
        this.canvasBg = canvasBg;
        this.canvasFg = canvasFg;
        this.type = type;
        this.data = data;
        this.options = this.mergeOptions(userOptions);
        this.validateOptions(this.options);
        this.init();
    }

    private mergeOptions(userOptions: Partial<ChartOptions>): Required<ChartOptions> {
        const merged = { ...Chart.DEFAULT_OPTIONS, ...userOptions };
        
        // 动态计算 cursorBounds 如果没有提供
        if (!userOptions.cursorBounds) {
            merged.cursorBounds = {
                min: merged.margin.left,
                max: this.canvasFg.width - merged.margin.right
            };
        }
        
        return merged;
    }

    private validateOptions(options: Partial<ChartOptions>): void {
        if (options.duration && options.duration <= 0) {
            throw new Error("动画时长必须大于0");
        }
        if (options.pathAnimationDuration && options.pathAnimationDuration <= 0) {
            throw new Error("路径动画时长必须大于0");
        }
        if (options.cursorBounds) {
            const { min, max } = options.cursorBounds;
            if (min >= max) {
                console.warn('游标边界设置无效，使用默认值');
            }
        }
    }

    private init() {
        this.initScales();
        this.renderer = new ChartRenderer(this.canvasBg, this.canvasFg);
        this.dataProcessor = new DataProcessor(this.options.useWorker);
        this.dataProcessor.setData(this.data);
        this.initMouseInteraction();
        this.drawBackground();
        this.animateData();
    }

    /** 运行时更新配置 */
    updateOptions(newOptions: Partial<ChartOptions>): void {
        this.validateOptions(newOptions);
        const oldOptions = { ...this.options };
        this.options = this.mergeOptions({ ...this.options, ...newOptions });

        // Worker 开关变化时，重建 Worker
        const workerChanged = newOptions.useWorker !== undefined && newOptions.useWorker !== oldOptions.useWorker;
        if (workerChanged && this.dataProcessor) {
            this.dataProcessor.setUseWorker(this.options.useWorker);
        }

        // 检查是否需要重新启动动画
        const animationPropsChanged =
            (newOptions.duration !== undefined && newOptions.duration !== oldOptions.duration) ||
            (newOptions.pathAnimationDuration !== undefined && newOptions.pathAnimationDuration !== oldOptions.pathAnimationDuration);

        // 检查缩放相关参数是否变化
        const zoomPropsChanged =
            (newOptions.zoomLevel !== undefined && newOptions.zoomLevel !== oldOptions.zoomLevel) ||
            (newOptions.enableZoom !== undefined && newOptions.enableZoom !== oldOptions.enableZoom) ||
            (newOptions.viewOffsetX !== undefined && newOptions.viewOffsetX !== oldOptions.viewOffsetX) ||
            (newOptions.viewOffsetY !== undefined && newOptions.viewOffsetY !== oldOptions.viewOffsetY);

        // 如果缩放参数变化，需要重新初始化比例尺
        if (zoomPropsChanged) {
            this.initScales();
            this.drawBackground();
        }

        // 如果其他参数变化，也需要重新绘制背景
        if (!zoomPropsChanged) {
            this.initScales();
            this.drawBackground();
        }

        if (animationPropsChanged) {
            gsap.killTweensOf(this);
            gsap.killTweensOf(this.data);
            this.pathProgress = 0;
            this.data.forEach(d => d.currentY = d.y * 0.1);
            this.animateData();
        } else {
            this.draw();
        }
    }

    /** 获取当前配置 */
    getOptions(): Required<ChartOptions> {
        return { ...this.options };
    }

    /** 获取图表绘制区域 */
    getChartArea() {
        const { margin } = this.options;
        return {
            top: margin.top,
            bottom: this.canvasBg.height - margin.bottom,
            left: margin.left,
            right: this.canvasBg.width - margin.right,
            width: this.canvasBg.width - margin.left - margin.right,
            height: this.canvasBg.height - margin.top - margin.bottom
        };
    }

    /** -------------------- 事件系统 -------------------- */
    on(event: ChartEvent, callback: EventCallback) {
        if (!this.events[event]) this.events[event] = [];
        this.events[event].push(callback);
    }

    private emit(event: ChartEvent, payload?: any) {
        const cbs = this.events[event];
        if (!cbs) return;
        cbs.forEach(cb => cb(payload));
    }

    /** -------------------- 比例尺 -------------------- */
    initScales() {
        const { margin, zoomLevel = 1, viewOffsetX = 0, viewOffsetY = 0, enableZoom = false } = this.options;
        const width = this.canvasBg.width - margin.left - margin.right;
        const height = this.canvasBg.height - margin.top - margin.bottom;

        console.log('initScales called with:', { zoomLevel, viewOffsetX, viewOffsetY, enableZoom });

        const xMax = d3.max(this.data, (d: ChartData) => d.x);
        const yMax = d3.max(this.data, (d: ChartData) => d.y);

        if (xMax === undefined || yMax === undefined) {
            throw new Error("数据中包含无效值");
        }

        if (!enableZoom || Math.abs(zoomLevel - 1) < 0.001) {
            // 正常缩放级别或未启用缩放，不应用缩放
            this.xScale = d3.scaleLinear()
                .domain([0, xMax])
                .range([margin.left, margin.left + width]);

            this.yScale = d3.scaleLinear()
                .domain([0, yMax])
                .range([margin.top + height, margin.top]);
        } else {
            // 应用缩放：缩放级别越大，显示的数据范围越小（放大效果）
            const zoomFactor = 1 / zoomLevel;

            // 计算可见区域的数据范围
            const visibleXRange = xMax * zoomFactor;
            const visibleYRange = yMax * zoomFactor;

            // 将像素偏移转换为数据偏移
            // 偏移量应该相对于可见范围进行缩放
            const offsetXData = (viewOffsetX / width) * visibleXRange;
            const offsetYData = -(viewOffsetY / height) * visibleYRange; // Y轴方向相反

            // 计算可见区域的中心点（基于偏移）
            const centerX = xMax / 2 + offsetXData;
            const centerY = yMax / 2 + offsetYData;

            // 计算可见区域的边界
            const halfVisibleX = visibleXRange / 2;
            const halfVisibleY = visibleYRange / 2;

            // 应用边界限制，确保不超出数据范围
            let xMin = centerX - halfVisibleX;
            let xMaxVisible = centerX + halfVisibleX;
            let yMin = centerY - halfVisibleY;
            let yMaxVisible = centerY + halfVisibleY;

            // 边界检查和调整
            if (xMin < 0) {
                xMaxVisible += -xMin;
                xMin = 0;
            }
            if (xMaxVisible > xMax) {
                xMin -= (xMaxVisible - xMax);
                xMaxVisible = xMax;
                xMin = Math.max(0, xMin);
            }

            if (yMin < 0) {
                yMaxVisible += -yMin;
                yMin = 0;
            }
            if (yMaxVisible > yMax) {
                yMin -= (yMaxVisible - yMax);
                yMaxVisible = yMax;
                yMin = Math.max(0, yMin);
            }

            console.log('缩放计算:', {
                zoomLevel,
                zoomFactor,
                visibleXRange,
                offsetXData,
                centerX,
                xMin,
                xMaxVisible,
                viewOffsetX,
                viewOffsetY
            });

            // 创建比例尺
            this.xScale = d3.scaleLinear()
                .domain([xMin, xMaxVisible])
                .range([margin.left, margin.left + width]);

            this.yScale = d3.scaleLinear()
                .domain([yMin, yMaxVisible])
                .range([margin.top + height, margin.top]);
        }
    }

    /** -------------------- 绘制静态层 -------------------- */
    drawBackground() {
        const state = {
            canvasBg: this.canvasBg,
            canvasFg: this.canvasFg,
            xScale: this.xScale,
            yScale: this.yScale,
            data: this.data,
            elements: this.elements,
            options: this.options,
            pathProgress: this.pathProgress,
        };
        this.renderer.drawBackground(state as any);
    }







    /** -------------------- 绘制动态层 -------------------- */
    draw() {
        const { cursorFollowCurve, margin } = this.options;

        // 根据开关，更新游标高度到曲线（渲染前同步元素状态）
        if (cursorFollowCurve) {
            for (const id in this.elements) {
                const el = this.elements[id] as any;
                if (el && 'startY' in el && 'endY' in el) {
                    const dataX = this.xScale.invert(el.x);
                    const currentY = this.getInterpolatedCurrentYAtDataX(dataX);
                    const yPix = this.yScale(currentY);
                    el.startY = margin.top;
                    el.endY = yPix;
                }
            }
        }

        const state = {
            canvasBg: this.canvasBg,
            canvasFg: this.canvasFg,
            xScale: this.xScale,
            yScale: this.yScale,
            data: this.data,
            elements: this.elements,
            options: this.options,
            pathProgress: this.pathProgress,
        };
        this.renderer.render(state as any);
    }









    /** -------------------- 数据动画 -------------------- */
    updateData(newData: ChartData[]) {
        this.data = newData;
        if (this.dataProcessor) this.dataProcessor.setData(this.data);
        this.initScales();
        this.drawBackground();
        this.animateData();
    }

    animateData() {
        const { duration, pathAnimation, pathAnimationDuration } = this.options;

        // 初始化动画起始值
        this.data.forEach(d => d.currentY = d.y * 0.1);
        this.pathProgress = 0;

        if (pathAnimation) {
            // 两阶段动画：先数据动画，再路径动画
            gsap.to(this.data, {
                duration,
                currentY: (i: number) => this.data[i].y,
                ease: "power2.out",
                onUpdate: () => {
                    // 在数据动画期间不绘制路径
                    if (this.pathProgress === 0) {
                        this.draw();
                    }
                },
                onComplete: () => {
                    // 第二阶段：路径绘制动画
                    gsap.to(this, {
                        duration: pathAnimationDuration,
                        pathProgress: 1,
                        ease: "none",
                        onUpdate: () => this.draw(),
                        onComplete: () => this.emit("dataAnimationComplete")
                    });
                }
            });
        } else {
            // 常规动画模式
            this.pathProgress = 1;
            gsap.to(this.data, {
                duration,
                currentY: (i: number) => this.data[i].y,
                ease: "power2.out",
                onUpdate: () => this.draw(),
                onComplete: () => this.emit("dataAnimationComplete")
            });
        }
    }

    /** -------------------- 交互元素管理 -------------------- */
    addElement(el: ChartElement) { this.elements[el.id] = el; }
    getElement(id: string) { return this.elements[id]; }
    removeElement(id: string) { delete this.elements[id]; }
    
    /** 实现 ChartLike 接口 */
    getAllElements() { return this.elements; }
    
    getDataCoordinates(screenX: number, screenY: number) {
        if (!this.xScale || !this.yScale) {
            return { x: screenX, y: screenY };
        }
        
        try {
            const dataX = this.xScale.invert(screenX);
            const dataY = this.yScale.invert(screenY);
            return { x: dataX, y: dataY };
        } catch (error) {
            return { x: screenX, y: screenY };
        }
    }

    /** -------------------- 简化的交互系统 -------------------- */
    /** 初始化基础交互（现在主要由 composable 处理） */
    initMouseInteraction() {
        // 基础交互现在由 useCanvasMouse composable 处理
        console.log('图表交互系统已初始化，使用 useCanvasMouse composable 进行交互');
    }

    /** 处理元素拖拽（由外部调用） */
    handleElementDrag(elementId: string, newX: number) {
        const element = this.getElement(elementId);
        if (!element) return;

        const { cursorBounds, cursorFollowCurve, margin } = this.options;
        const clampedX = Math.max(cursorBounds.min, Math.min(cursorBounds.max, newX));
        element.x = clampedX;

        if (cursorFollowCurve) {
            const dataX = this.xScale.invert(clampedX);
            // 统一通过 DataProcessor（内部按 useWorker 决定是否走 Worker）
            this.dataProcessor.interpolateAtX(dataX).then((currentY: number) => {
                const yPix = this.yScale(currentY);
                (element as any).startY = margin.top;
                (element as any).endY = yPix;
                this.draw();
                this.emit('cursorMove', clampedX);
            });
            return;
        }

        this.draw();
        this.emit('cursorMove', clampedX);
    }

    /** 绑定游标拖拽 */
    bindCursorDrag(cursorId: string) {
        // 这个方法现在由内置的鼠标交互系统处理
        console.log(`游标 ${cursorId} 拖拽已启用`);
    }

    /** -------------------- 清理 -------------------- */
    destroy() {
        // 停止所有动画
        gsap.killTweensOf(this);
        gsap.killTweensOf(this.data);
        if (this.dataProcessor) this.dataProcessor.dispose();
        
        // 清理元素和事件（事件监听器由 composable 管理）
        this.elements = {};
        this.events = {};
    }
}