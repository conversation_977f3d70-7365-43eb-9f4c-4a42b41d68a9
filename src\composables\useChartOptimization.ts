/**
 * 图表性能优化相关的组合式函数
 * 使用 VueUse 提供的工具来优化性能和用户体验
 */
import { ref, computed, watch } from 'vue'
import { 
  useRafFn, 
  useDebounceFn, 
  useThrottleFn,
  usePerformanceObserver,
  useMemory,
  useFps,
  useElementSize,
  useResizeObserver,
  useIntersectionObserver,
  usePreferredReducedMotion
} from '@vueuse/core'

export interface ChartPerformanceMetrics {
  fps: number
  memory?: MemoryInfo
  renderTime: number
  lastUpdateTime: number
}

export function useChartOptimization(canvasRef: Ref<HTMLCanvasElement | null>) {
  // 性能监控
  const { fps } = useFps()
  const memory = useMemory()
  const preferredReducedMotion = usePreferredReducedMotion()
  
  // 元素尺寸监控
  const { width, height } = useElementSize(canvasRef)
  
  // 性能指标
  const renderTime = ref(0)
  const lastUpdateTime = ref(0)
  const isVisible = ref(true)
  
  // 性能指标计算
  const performanceMetrics = computed<ChartPerformanceMetrics>(() => ({
    fps: fps.value,
    memory: memory.value,
    renderTime: renderTime.value,
    lastUpdateTime: lastUpdateTime.value
  }))
  
  // 自适应质量设置
  const adaptiveQuality = computed(() => {
    const currentFps = fps.value
    if (currentFps < 30) {
      return 'low' // 低质量模式
    } else if (currentFps < 50) {
      return 'medium' // 中等质量模式
    } else {
      return 'high' // 高质量模式
    }
  })
  
  // 智能防抖/节流
  const createSmartDebounce = (fn: Function, baseDelay: number = 16) => {
    const delay = computed(() => {
      // 根据FPS动态调整防抖延迟
      const currentFps = fps.value
      if (currentFps < 30) {
        return baseDelay * 2 // 性能差时增加延迟
      } else if (currentFps > 50) {
        return baseDelay * 0.5 // 性能好时减少延迟
      }
      return baseDelay
    })
    
    return useDebounceFn(fn, delay)
  }
  
  const createSmartThrottle = (fn: Function, baseDelay: number = 16) => {
    const delay = computed(() => {
      const currentFps = fps.value
      if (currentFps < 30) {
        return baseDelay * 2
      } else if (currentFps > 50) {
        return baseDelay * 0.5
      }
      return baseDelay
    })
    
    return useThrottleFn(fn, delay)
  }
  
  // RAF 优化渲染
  const createOptimizedRender = (renderFn: Function) => {
    let lastRenderTime = 0
    
    const { pause, resume, isActive } = useRafFn(() => {
      const now = performance.now()
      const deltaTime = now - lastRenderTime
      
      // 记录渲染时间
      const renderStart = performance.now()
      renderFn(deltaTime)
      renderTime.value = performance.now() - renderStart
      
      lastUpdateTime.value = now
      lastRenderTime = now
    }, { immediate: false })
    
    return { pause, resume, isActive }
  }
  
  // 可见性优化
  useIntersectionObserver(
    canvasRef,
    ([{ isIntersecting }]) => {
      isVisible.value = isIntersecting
    },
    {
      threshold: 0.1 // 当10%可见时开始渲染
    }
  )
  
  // 尺寸变化优化
  const onResize = useDebounceFn((newWidth: number, newHeight: number) => {
    // 尺寸变化时的优化处理
    console.log('Canvas resized:', { width: newWidth, height: newHeight })
  }, 100)
  
  watch([width, height], ([newWidth, newHeight]) => {
    onResize(newWidth, newHeight)
  })
  
  // 性能警告
  const performanceWarnings = computed(() => {
    const warnings: string[] = []
    
    if (fps.value < 30) {
      warnings.push('FPS过低，建议降低图表复杂度')
    }
    
    if (memory.value && memory.value.usedJSHeapSize > 50 * 1024 * 1024) {
      warnings.push('内存使用过高，建议优化数据处理')
    }
    
    if (renderTime.value > 16) {
      warnings.push('渲染时间过长，建议使用Web Worker')
    }
    
    return warnings
  })
  
  // 动画偏好设置
  const shouldUseAnimation = computed(() => {
    return preferredReducedMotion.value !== 'reduce'
  })
  
  // 自适应配置
  const adaptiveConfig = computed(() => ({
    // 根据性能调整配置
    enableAnimations: shouldUseAnimation.value && fps.value > 30,
    useWebWorker: fps.value < 40 || (memory.value?.usedJSHeapSize || 0) > 30 * 1024 * 1024,
    renderQuality: adaptiveQuality.value,
    maxDataPoints: fps.value < 30 ? 100 : fps.value < 50 ? 500 : 1000,
    animationDuration: fps.value < 30 ? 0.5 : fps.value < 50 ? 1 : 1.5
  }))
  
  return {
    // 性能指标
    performanceMetrics,
    performanceWarnings,
    adaptiveQuality,
    adaptiveConfig,
    
    // 状态
    isVisible,
    shouldUseAnimation,
    
    // 尺寸
    width,
    height,
    
    // 优化工具
    createSmartDebounce,
    createSmartThrottle,
    createOptimizedRender,
    
    // 事件
    onResize
  }
}

/**
 * 图表数据优化
 */
export function useChartDataOptimization() {
  // 数据缓存
  const dataCache = new Map<string, any>()
  
  // 智能数据抽稀
  const downsampleData = (data: any[], maxPoints: number = 1000) => {
    if (data.length <= maxPoints) return data
    
    const step = Math.ceil(data.length / maxPoints)
    return data.filter((_, index) => index % step === 0)
  }
  
  // 数据预处理
  const preprocessData = useDebounceFn((data: any[], operations: string[] = []) => {
    const cacheKey = JSON.stringify({ data: data.slice(0, 10), operations })
    
    if (dataCache.has(cacheKey)) {
      return dataCache.get(cacheKey)
    }
    
    let result = [...data]
    
    // 应用各种预处理操作
    for (const op of operations) {
      switch (op) {
        case 'downsample':
          result = downsampleData(result)
          break
        case 'smooth':
          // 简单平滑处理
          if (result.length > 2) {
            result = result.map((point, i) => {
              if (i === 0 || i === result.length - 1) return point
              const prev = result[i - 1]
              const next = result[i + 1]
              return {
                ...point,
                y: (prev.y + point.y + next.y) / 3
              }
            })
          }
          break
      }
    }
    
    dataCache.set(cacheKey, result)
    return result
  }, 100)
  
  // 清理缓存
  const clearCache = () => {
    dataCache.clear()
  }
  
  return {
    downsampleData,
    preprocessData,
    clearCache,
    cacheSize: computed(() => dataCache.size)
  }
}
