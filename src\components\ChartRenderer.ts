/**
 * ChartRenderer: 负责所有 Canvas 绘制
 * 只依赖传入的状态，不保存业务数据
 */
import * as d3 from 'd3';

export interface RenderState {
  canvasBg: HTMLCanvasElement;
  canvasFg: HTMLCanvasElement;
  xScale: d3.ScaleLinear<number, number>;
  yScale: d3.ScaleLinear<number, number>;
  data: { x: number; y: number; currentY?: number }[];
  elements: { [key: string]: { draw(ctx: CanvasRenderingContext2D): void } };
  options: Required<{
    margin: { top: number; right: number; bottom: number; left: number };
    color: string;
    duration: number;
    strokeWidth: number;
    showPoints: boolean;
    pointRadius: number;
    showGrid: boolean;
    showAxisLabels: boolean;
    axisLabelColor: string;
    axisLabelFont: string;
    smooth: boolean;
    curveType: 'linear' | 'cardinal' | 'basis' | 'monotone';
    pathAnimation: boolean;
    pathAnimationDuration: number;
    cursorBounds: { min: number; max: number };
    enableDrag: boolean;
    hoverEffects: boolean;
    cursorFollowCurve: boolean;
    useWorker: boolean;
  }>;
  pathProgress: number; // 0..1
}

export class ChartRenderer {
  private ctxBg: CanvasRenderingContext2D;
  private ctxFg: CanvasRenderingContext2D;

  constructor(canvasBg: HTMLCanvasElement, canvasFg: HTMLCanvasElement) {
    const ctxBg = canvasBg.getContext('2d');
    const ctxFg = canvasFg.getContext('2d');
    if (!ctxBg || !ctxFg) throw new Error('Canvas 2D context not available');
    this.ctxBg = ctxBg;
    this.ctxFg = ctxFg;
  }

  drawBackground(state: RenderState) {
    const { canvasBg, options } = state;
    const { margin, showGrid, showAxisLabels, axisLabelColor, axisLabelFont } = options;

    this.ctxBg.clearRect(0, 0, canvasBg.width, canvasBg.height);

    if (showGrid) this.drawGrid(state);
    this.drawAxes(state);
    if (showAxisLabels) this.drawAxisLabels(state, axisLabelColor, axisLabelFont);
  }

  render(state: RenderState) {
    const { canvasFg } = state;
    this.ctxFg.clearRect(0, 0, canvasFg.width, canvasFg.height);

    // 折线（支持平滑 & 路径动画）
    if (state.options.smooth) {
      this.drawSmoothLine(state);
    } else {
      this.drawStraightLine(state);
    }

    if (state.options.showPoints) {
      this.drawDataPoints(state);
    }

    // 交互元素
    this.drawElements(state);
  }

  private drawGrid(state: RenderState) {
    const { canvasBg, options, xScale, yScale } = state;
    const { margin } = options;

    const width = canvasBg.width - margin.left - margin.right;
    const height = canvasBg.height - margin.top - margin.bottom;

    this.ctxBg.save();
    this.ctxBg.strokeStyle = 'rgba(0,0,0,0.1)';
    this.ctxBg.lineWidth = 1;

    // 纵向网格
    const xTicks = xScale.ticks(10);
    xTicks.forEach((t) => {
      const x = xScale(t);
      this.ctxBg.beginPath();
      this.ctxBg.moveTo(x, margin.top);
      this.ctxBg.lineTo(x, margin.top + height);
      this.ctxBg.stroke();
    });

    // 横向网格
    const yTicks = yScale.ticks(8);
    yTicks.forEach((t) => {
      const y = yScale(t);
      this.ctxBg.beginPath();
      this.ctxBg.moveTo(margin.left, y);
      this.ctxBg.lineTo(margin.left + width, y);
      this.ctxBg.stroke();
    });

    this.ctxBg.restore();
  }

  private drawAxes(state: RenderState) {
    const { canvasBg, options } = state;
    const { margin } = options;
    const width = canvasBg.width - margin.left - margin.right;
    const height = canvasBg.height - margin.top - margin.bottom;

    this.ctxBg.save();
    this.ctxBg.strokeStyle = 'rgba(0,0,0,0.5)';
    this.ctxBg.lineWidth = 1.5;

    // X 轴
    this.ctxBg.beginPath();
    this.ctxBg.moveTo(margin.left, margin.top + height);
    this.ctxBg.lineTo(margin.left + width, margin.top + height);
    this.ctxBg.stroke();

    // X轴箭头
    const xEnd = margin.left + width;
    this.ctxBg.beginPath();
    this.ctxBg.moveTo(xEnd, margin.top + height);
    this.ctxBg.lineTo(xEnd - 10, margin.top + height - 5);
    this.ctxBg.lineTo(xEnd - 10, margin.top + height + 5);
    this.ctxBg.closePath();
    this.ctxBg.fill();

    // Y 轴
    this.ctxBg.beginPath();
    this.ctxBg.moveTo(margin.left, margin.top);
    this.ctxBg.lineTo(margin.left, margin.top + height);
    this.ctxBg.stroke();

    // Y轴箭头
    this.ctxBg.beginPath();
    this.ctxBg.moveTo(margin.left, margin.top);
    this.ctxBg.lineTo(margin.left - 5, margin.top + 10);
    this.ctxBg.lineTo(margin.left + 5, margin.top + 10);
    this.ctxBg.closePath();
    this.ctxBg.fill();

    this.ctxBg.restore();
  }

  private drawAxisLabels(state: RenderState, color: string, font: string) {
    const { canvasBg, options, xScale, yScale } = state;
    const { margin } = options;

    const width = canvasBg.width - margin.left - margin.right;
    const height = canvasBg.height - margin.top - margin.bottom;

    this.ctxBg.save();
    this.ctxBg.fillStyle = color;
    this.ctxBg.font = font;
    this.ctxBg.textAlign = 'center';
    this.ctxBg.textBaseline = 'top';

    // X 轴刻度
    const xTicks = xScale.ticks(10);
    xTicks.forEach((t) => {
      const x = xScale(t);
      this.ctxBg.fillText(String(t), x, margin.top + height + 6);
    });

    // Y 轴刻度
    this.ctxBg.textAlign = 'right';
    this.ctxBg.textBaseline = 'middle';
    const yTicks = yScale.ticks(8);
    yTicks.forEach((t) => {
      const y = yScale(t);
      this.ctxBg.fillText(String(t), margin.left - 6, y);
    });

    this.ctxBg.restore();
  }

  private drawStraightLine(state: RenderState) {
    const { xScale, yScale, data, options, pathProgress } = state;
    const { color, strokeWidth } = options;

    this.ctxFg.save();
    this.ctxFg.strokeStyle = color;
    this.ctxFg.lineWidth = strokeWidth;
    this.ctxFg.lineJoin = 'round';
    this.ctxFg.lineCap = 'round';

    const n = data.length;
    if (n < 2) {
      this.ctxFg.restore();
      return;
    }
    const p = Math.max(0, Math.min(1, pathProgress || 1));
    const t = p * (n - 1);
    const k = Math.floor(t);
    const f = t - k;

    this.ctxFg.beginPath();
    // 起点
    let x0 = xScale(data[0].x);
    let y0 = yScale(data[0].currentY ?? data[0].y);
    this.ctxFg.moveTo(x0, y0);

    // 先画完整的段 0..k
    for (let i = 1; i <= Math.min(k, n - 1); i++) {
      const xi = xScale(data[i].x);
      const yi = yScale(data[i].currentY ?? data[i].y);
      this.ctxFg.lineTo(xi, yi);
    }

    // 再画最后一段的部分（插值到 k..k+1 之间）
    if (k < n - 1) {
      const a = data[k];
      const b = data[k + 1];
      const ax = a.x, bx = b.x;
      const ay = a.currentY ?? a.y;
      const by = b.currentY ?? b.y;
      const xi = xScale(ax + (bx - ax) * f);
      const yi = yScale(ay + (by - ay) * f);
      this.ctxFg.lineTo(xi, yi);
    }

    this.ctxFg.stroke();
    this.ctxFg.restore();
  }

  private drawSmoothLine(state: RenderState) {
    const { xScale, yScale, data, options, pathProgress } = state;
    const { color, strokeWidth, curveType } = options;

    const line = d3.line<{ x: number; y: number; currentY?: number }>()
      .x((d) => xScale(d.x))
      .y((d) => yScale(d.currentY ?? d.y));

    switch (curveType) {
      case 'cardinal': line.curve(d3.curveCardinal); break;
      case 'basis': line.curve(d3.curveBasis); break;
      case 'monotone': line.curve(d3.curveMonotoneX); break;
      default: break;
    }

    const n = data.length;
    if (n < 2) return;

    const p = Math.max(0, Math.min(1, pathProgress || 1));
    let subData: { x: number; y: number; currentY?: number }[];

    if (p >= 1) {
      subData = data;
    } else {
      const t = p * (n - 1);
      const k = Math.floor(t);
      const f = t - k;

      const a = data[k];
      const b = data[Math.min(k + 1, n - 1)];
      const ay = a.currentY ?? a.y;
      const by = b.currentY ?? b.y;
      const ix = a.x + (b.x - a.x) * f;
      const iy = ay + (by - ay) * f;

      subData = data.slice(0, k + 1).concat([{ x: ix, y: iy, currentY: iy }]);
    }

    const pathData = line(subData);
    if (!pathData) return;

    this.ctxFg.save();
    this.ctxFg.strokeStyle = color;
    this.ctxFg.lineWidth = strokeWidth;
    this.ctxFg.lineJoin = 'round';
    this.ctxFg.lineCap = 'round';

    const path2D = new Path2D(pathData);
    this.ctxFg.stroke(path2D);
    this.ctxFg.restore();
  }

  private drawDataPoints(state: RenderState) {
    const { xScale, yScale, data, options } = state;
    const { color, pointRadius } = options;

    this.ctxFg.save();
    this.ctxFg.fillStyle = color;
    data.forEach((d) => {
      const x = xScale(d.x);
      const y = yScale(d.currentY ?? d.y);
      this.ctxFg.beginPath();
      this.ctxFg.arc(x, y, pointRadius, 0, Math.PI * 2);
      this.ctxFg.fill();
    });
    this.ctxFg.restore();
  }

  private drawElements(state: RenderState) {
    const { elements } = state;
    Object.keys(elements).forEach((id) => {
      elements[id]?.draw(this.ctxFg);
    });
  }
}