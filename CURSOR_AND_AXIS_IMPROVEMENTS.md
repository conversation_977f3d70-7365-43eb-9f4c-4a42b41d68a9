# 游标和坐标轴改进总结

## 🎯 问题解决

### 1. ✅ 游标边界限制问题
**问题**: 游标在X轴下方时仍然可视
**解决方案**: 
- 在游标绘制时添加图表区域裁剪
- 只在游标线在图表区域内时绘制箭头
- 限制游标绘制范围在 `chartBounds` 内

```typescript
// 限制游标在图表区域内绘制
const clampedStartY = Math.max(this.chartBounds.top, Math.min(this.chartBounds.bottom, this.startY));
const clampedEndY = Math.max(this.chartBounds.top, Math.min(this.chartBounds.bottom, this.endY));

// 如果游标完全在图表区域外，则不绘制
if (this.x < this.chartBounds.left || this.x > this.chartBounds.right) {
    return;
}
```

### 2. ✅ 游标拖拽冲突问题
**问题**: 缩放后拖拽游标和图表可视区域发生冲突
**解决方案**:
- 添加 `isDraggingCursor` 状态区分拖拽类型
- 在鼠标按下时检测点击目标（游标 vs 背景）
- 分别处理游标拖拽和视图拖拽

```typescript
// 检查是否点击了游标
const cursor = chart.value.getElement('main-cursor') as Cursor
if (cursor && cursor.isPointInside(mouseX, mouseY)) {
  // 激活游标并开始拖拽
  cursor.setActive(true)
  isDraggingCursor.value = true
  return
}

// 否则处理视图拖拽
isDraggingView.value = true
```

### 3. ✅ 游标激活态/选中态
**问题**: 需要游标选中态和自定义颜色
**解决方案**:
- 添加 `isActive` 状态和 `setActive()` 方法
- 支持自定义默认颜色和激活颜色
- 激活态时显示更粗的线条和选中指示器

```typescript
// 激活态视觉效果
const currentColor = this.isActive ? this.activeColor : this.color;
const currentWidth = this.isActive ? this.activeWidth : this.width;

// 激活态时添加选中指示器
if (this.isActive) {
    ctx.beginPath();
    ctx.arc(this.x, clampedStartY, 4, 0, 2 * Math.PI);
    ctx.fillStyle = this.activeColor;
    ctx.fill();
}
```

### 4. ✅ 坐标轴显示范围扩展
**问题**: X轴和Y轴显示极限只到数据末尾，显示效果不佳
**解决方案**:
- 添加 `xAxisPadding` 和 `yAxisPadding` 配置
- 自动扩展坐标轴范围，默认X轴扩展5%，Y轴扩展10%
- 支持自定义扩展比例

```typescript
// 计算扩展后的范围
const xRange = xMax - xMin;
const yRange = yMax - yMin;
const xPadding = xRange * xAxisPadding;
const yPadding = yRange * yAxisPadding;

const extendedXMin = Math.max(0, xMin - xPadding);
const extendedXMax = xMax + xPadding;
const extendedYMin = Math.max(0, yMin - yPadding);
const extendedYMax = yMax + yPadding;
```

## 🛠️ 新增功能

### 游标配置选项
```typescript
interface CursorOptions {
  cursorColor: string        // 游标默认颜色 (默认: '#ff6b6b')
  cursorActiveColor: string  // 游标激活颜色 (默认: '#ff3333')
  cursorWidth: number        // 游标默认宽度 (默认: 2)
  cursorActiveWidth: number  // 游标激活宽度 (默认: 3)
}
```

### 坐标轴范围配置
```typescript
interface AxisRangeOptions {
  xAxisPadding: number  // X轴范围扩展比例 (默认: 0.05 = 5%)
  yAxisPadding: number  // Y轴范围扩展比例 (默认: 0.1 = 10%)
}
```

## 🎮 用户界面改进

### 新增控制面板区域

#### 游标配置
- **游标颜色**: 颜色选择器
- **激活颜色**: 颜色选择器  
- **游标宽度**: 数值输入 (1-10)
- **激活宽度**: 数值输入 (1-10)

#### 坐标轴范围
- **X轴扩展**: 百分比滑块 (0-50%)
- **Y轴扩展**: 百分比滑块 (0-50%)

### 交互改进
- **点击游标**: 激活游标（显示选中状态）
- **拖拽游标**: 移动游标位置（不影响视图）
- **点击背景**: 取消游标激活，开始视图拖拽
- **Ctrl+Z**: 重置缩放和游标状态

## 🔧 技术实现

### 游标类增强
```typescript
export class Cursor extends ChartElement {
    public isActive: boolean;
    public activeColor: string;
    public activeWidth: number;
    public chartBounds: { top: number; bottom: number; left: number; right: number };
    
    setActive(active: boolean): void;
    updateBounds(bounds: object): void;
    isPointInside(x: number, y: number): boolean; // 扩大激活态点击区域
}
```

### 图表引擎新增方法
```typescript
class Chart {
    getXScale(): d3.ScaleLinear<number, number>;
    getYScale(): d3.ScaleLinear<number, number>;
    getDataProcessor(): DataProcessor;
    getElement(id: string): ChartElement | undefined;
}
```

### 状态管理
```typescript
// 拖拽状态区分
const isDraggingView = ref(false)      // 视图拖拽
const isDraggingCursor = ref(false)    // 游标拖拽

// 配置持久化
const cursorColor = useLocalStorage('chart-cursor-color', '#ff6b6b')
const xAxisPadding = useLocalStorage('chart-x-axis-padding', 0.05)
```

## 📊 使用示例

### 基础配置
```vue
<template>
  <Charts 
    :data="chartData"
    :options="{
      cursorColor: '#ff6b6b',
      cursorActiveColor: '#ff3333',
      xAxisPadding: 0.05,
      yAxisPadding: 0.1
    }"
  />
</template>
```

### 自定义游标样式
```vue
<template>
  <Charts 
    :data="chartData"
    :options="{
      cursorColor: '#2196f3',
      cursorActiveColor: '#1976d2',
      cursorWidth: 3,
      cursorActiveWidth: 5
    }"
  />
</template>
```

### 自定义坐标轴范围
```vue
<template>
  <Charts 
    :data="chartData"
    :options="{
      xAxisPadding: 0.1,  // X轴扩展10%
      yAxisPadding: 0.15  // Y轴扩展15%
    }"
  />
</template>
```

## 🎯 最佳实践

### 游标使用建议
1. **颜色对比**: 确保游标颜色与图表背景有足够对比度
2. **激活反馈**: 使用明显不同的激活颜色提供视觉反馈
3. **宽度设置**: 根据图表复杂度调整游标宽度

### 坐标轴范围建议
1. **数据密集**: 较小的扩展比例 (2-5%)
2. **数据稀疏**: 较大的扩展比例 (10-20%)
3. **演示用途**: 适中的扩展比例 (5-10%)

## 🚀 性能优化

### 渲染优化
- 游标只在图表区域内绘制
- 使用裁剪区域避免无效绘制
- 智能的重绘触发机制

### 交互优化
- 防抖的鼠标移动处理
- 区分不同类型的拖拽操作
- 优化的点击检测算法

## 🔮 未来扩展

计划中的功能：
1. **多游标支持**: 支持多个独立的游标
2. **游标样式**: 更多游标样式选项（虚线、点线等）
3. **智能吸附**: 游标自动吸附到数据点
4. **范围选择**: 支持范围选择功能
5. **动画效果**: 游标移动的平滑动画
