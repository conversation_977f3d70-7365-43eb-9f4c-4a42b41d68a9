# 刻度和缩放功能说明

## 🎯 新增功能概览

### 1. 智能刻度系统
- **标准数学刻度**: 自动生成 1, 2, 5 的倍数刻度（如 0.1, 0.2, 0.5, 1, 2, 5, 10...）
- **自定义刻度间距**: 可以手动设置 X 轴和 Y 轴的刻度步长
- **智能标签格式化**: 根据刻度步长自动调整小数位数
- **可配置刻度数量**: 可以设置目标刻度数量

### 2. 精确缩放控制
- **百分比步进**: 缩放按 1% 步进，可自定义步进大小
- **可配置范围**: 可设置最小和最大缩放级别
- **以鼠标为中心**: 缩放以鼠标位置为中心点
- **边界限制**: 防止过度缩放

## 🛠️ 配置选项

### 刻度配置
```typescript
interface TickOptions {
  xTickCount: number        // X轴刻度数量 (默认: 10)
  yTickCount: number        // Y轴刻度数量 (默认: 8)
  customXStep: number       // 自定义X轴步长
  customYStep: number       // 自定义Y轴步长
  useCustomXStep: boolean   // 是否使用自定义X轴步长
  useCustomYStep: boolean   // 是否使用自定义Y轴步长
}
```

### 缩放配置
```typescript
interface ZoomOptions {
  zoomStep: number     // 缩放步进百分比 (默认: 1%)
  zoomMin: number      // 最小缩放级别 (默认: 0.1 = 10%)
  zoomMax: number      // 最大缩放级别 (默认: 5 = 500%)
}
```

## 📊 刻度系统详解

### 自动刻度计算
系统会自动计算最合适的刻度间距：

```typescript
// 示例：数据范围 0-100
// 目标刻度数量：10
// 计算结果：步长 = 10，刻度 = [0, 10, 20, 30, ..., 100]

// 示例：数据范围 0-0.7
// 目标刻度数量：8
// 计算结果：步长 = 0.1，刻度 = [0, 0.1, 0.2, ..., 0.7]
```

### 标准刻度倍数
系统使用标准的数学刻度倍数：
- **1 系列**: 1, 10, 100, 1000... 或 0.1, 0.01, 0.001...
- **2 系列**: 2, 20, 200, 2000... 或 0.2, 0.02, 0.002...
- **5 系列**: 5, 50, 500, 5000... 或 0.5, 0.05, 0.005...

### 自定义刻度步长
当启用自定义步长时：
```typescript
// 强制使用 0.25 作为步长
customXStep: 0.25
useCustomXStep: true

// 结果：[0, 0.25, 0.5, 0.75, 1.0, 1.25, ...]
```

## 🔍 缩放系统详解

### 百分比步进缩放
```typescript
// 当前缩放：100%
// 步进：1%
// 向上滚轮：100% → 101%
// 向下滚轮：100% → 99%

// 当前缩放：150%
// 步进：5%
// 向上滚轮：150% → 157.5%
// 向下滚轮：150% → 142.5%
```

### 缩放边界
```typescript
// 配置示例
zoomMin: 0.1    // 最小 10%
zoomMax: 5      // 最大 500%
zoomStep: 1     // 步进 1%

// 缩放会被限制在 10% - 500% 之间
```

## 🎮 用户界面

### 控制面板新增项目

#### 缩放配置区域
- **缩放步进**: 滑块调整步进百分比 (0.1% - 10%)
- **最小缩放**: 设置最小缩放级别
- **最大缩放**: 设置最大缩放级别

#### 刻度配置区域
- **X轴刻度数量**: 设置目标刻度数量 (2-20)
- **Y轴刻度数量**: 设置目标刻度数量 (2-20)
- **自定义X轴步长**: 开关 + 数值输入
- **自定义Y轴步长**: 开关 + 数值输入

### 实时预览
所有配置更改都会实时应用到图表，无需刷新页面。

## 💾 持久化存储

所有配置都会自动保存到浏览器本地存储：
- 刻度配置
- 缩放配置
- 用户偏好设置

下次打开页面时会自动恢复之前的设置。

## 🔧 使用示例

### 基础使用
```vue
<template>
  <Charts 
    :data="chartData"
    :options="{
      xTickCount: 12,
      yTickCount: 6,
      zoomStep: 2,
      zoomMin: 0.5,
      zoomMax: 3
    }"
  />
</template>
```

### 自定义刻度步长
```vue
<template>
  <Charts 
    :data="chartData"
    :options="{
      useCustomXStep: true,
      customXStep: 0.5,
      useCustomYStep: true,
      customYStep: 10
    }"
  />
</template>
```

### 精确缩放控制
```vue
<template>
  <Charts 
    :data="chartData"
    :options="{
      zoomStep: 0.5,    // 0.5% 步进
      zoomMin: 0.25,    // 最小 25%
      zoomMax: 10       // 最大 1000%
    }"
  />
</template>
```

## 🎯 最佳实践

### 刻度设置建议
1. **整数数据**: 使用自动刻度或整数步长
2. **小数数据**: 根据精度设置合适的小数步长
3. **百分比数据**: 使用 0.01, 0.05, 0.1 等步长
4. **时间数据**: 使用时间相关的步长

### 缩放设置建议
1. **详细查看**: 设置较小的步进 (0.5% - 1%)
2. **快速浏览**: 设置较大的步进 (2% - 5%)
3. **数据密集**: 允许更大的最大缩放
4. **性能考虑**: 限制最大缩放级别

## 🚀 性能优化

### 智能重绘
- 只有在刻度配置变化时才重新计算刻度
- 缩放时使用防抖优化
- 自动跳过不必要的重绘

### 内存管理
- 刻度计算结果会被缓存
- 自动清理过期的缓存数据
- 优化大数据集的处理

## 🔮 未来扩展

计划中的功能：
1. **对数刻度**: 支持对数坐标轴
2. **时间刻度**: 专门的时间轴刻度
3. **多轴支持**: 支持双Y轴
4. **刻度样式**: 自定义刻度线样式
5. **动态刻度**: 根据数据动态调整刻度
