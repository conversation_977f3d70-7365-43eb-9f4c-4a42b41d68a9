<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { Chart, Cursor, HighlightPoint } from './chartEngine'
import { useCanvasMouse } from '../composables/useChartMouse'
import type { ChartOptions } from './chartEngine'

// Props 定义
interface Props {
  data?: { x: number; y: number }[]
  type?: 'line' | 'bar'
  options?: Partial<ChartOptions>
  width?: number
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [
    { x: 0, y: 10 },
    { x: 1, y: 25 },
    { x: 2, y: 15 },
    { x: 3, y: 40 },
    { x: 4, y: 30 },
    { x: 5, y: 50 }
  ],
  type: 'line',
  options: () => ({}),
  width: 800,
  height: 400
})

// Emits 定义
const emit = defineEmits<{
  cursorMove: [x: number]
  dataAnimationComplete: []
  chartReady: [chart: any]
}>()

// 响应式引用
const canvasBg = ref<HTMLCanvasElement | null>(null)
const canvasFg = ref<HTMLCanvasElement | null>(null)
const chart = ref<Chart | null>(null)
const isReady = ref(false)

// 使用解耦后的鼠标交互组合式函数
const { mouseX, mouseY, isHovering, isDragging, hoveredElement, getDataCoordinates } = 
  useCanvasMouse(canvasFg, chart)

// 为了向后兼容，创建 getChartCoordinates 别名
const getChartCoordinates = getDataCoordinates

// 处理元素拖拽
watch([isDragging, hoveredElement, mouseX], () => {
  if (isDragging.value && hoveredElement.value && chart.value) {
    chart.value.handleElementDrag(hoveredElement.value, mouseX.value)
  }
})

// 控制面板状态
const showControls = ref(true)
const currentColor = ref('#4285f4')
const showPoints = ref(false)
const showGrid = ref(true)
const strokeWidth = ref(2)
const animationDuration = ref(1.5)
const smoothCurve = ref(false)
const curveType = ref('cardinal')
const pathAnimation = ref(true)
const pathAnimationDuration = ref(2)
const showAxisLabels = ref(true)
const cursorFollowCurve = ref(false)
const useWorker = ref(false)

// 缩放相关状态
const zoomLevel = ref(1) // 缩放级别 (0.5-3)
const viewOffsetX = ref(0) // 视图X偏移
const viewOffsetY = ref(0) // 视图Y偏移
const enableZoom = ref(true)
const isDraggingView = ref(false) // 是否正在拖拽视图
const lastMouseX = ref(0) // 鼠标上次X位置
const lastMouseY = ref(0) // 鼠标上次Y位置

// 初始化图表
function initChart() {
  if (!canvasBg.value || !canvasFg.value) return

  try {
    // 设置 Canvas 尺寸
    canvasBg.value.width = props.width
    canvasBg.value.height = props.height
    canvasFg.value.width = props.width
    canvasFg.value.height = props.height

    // 合并用户配置和控制面板配置
    const mergedOptions: Partial<ChartOptions> = {
      color: currentColor.value,
      showPoints: showPoints.value,
      showGrid: showGrid.value,
      strokeWidth: strokeWidth.value,
      duration: animationDuration.value,
      smooth: smoothCurve.value,
      curveType: curveType.value as 'linear' | 'cardinal' | 'basis' | 'monotone',
      pathAnimation: pathAnimation.value,
      pathAnimationDuration: pathAnimationDuration.value,
      showAxisLabels: showAxisLabels.value,
      cursorFollowCurve: cursorFollowCurve.value,
      useWorker: useWorker.value,
      zoomLevel: enableZoom.value ? zoomLevel.value : 1,
      enableZoom: enableZoom.value,
      viewOffsetX: enableZoom.value ? viewOffsetX.value : 0,
      viewOffsetY: enableZoom.value ? viewOffsetY.value : 0,
      ...props.options
    }

    // 创建图表实例
    chart.value = new Chart(
      canvasBg.value,
      canvasFg.value,
      props.type,
      props.data,
      mergedOptions
    )

    // 添加交互元素
    addInteractiveElements()

    // 绑定事件
    bindChartEvents()

    isReady.value = true
    emit('chartReady', chart.value)

  } catch (error) {
    console.error('图表初始化失败:', error)
  }
}

// 添加交互元素
function addInteractiveElements() {
  if (!chart.value) return

  // 获取图表绘制区域
  const chartArea = chart.value.getChartArea()
  
  // 添加游标，限制在图表区域内（从顶部到底部，不超出X轴）
  const cursor = new Cursor('main-cursor', 200, chartArea.top, chartArea.bottom)
  chart.value.addElement(cursor)
  chart.value.bindCursorDrag('main-cursor')

  // 输出游标信息到控制台
  console.log('游标创建完成:', {
    长度: cursor.getLength(),
    起始Y: cursor.startY,
    结束Y: cursor.endY,
    图表区域: chartArea
  })

  // 注释掉高亮点，保持图表简洁
  // const highlightPoint = new HighlightPoint('highlight-1', 300, 150, 6, 'orange')
  // chart.value.addElement(highlightPoint)
}

// 绑定图表事件
function bindChartEvents() {
  if (!chart.value) return

  chart.value.on('cursorMove', (x: number) => {
    emit('cursorMove', x)
  })

  chart.value.on('dataAnimationComplete', () => {
    emit('dataAnimationComplete')
  })
}

// 更新图表配置
function updateChartOptions() {
  if (!chart.value) return

  chart.value.updateOptions({
    color: currentColor.value,
    showPoints: showPoints.value,
    showGrid: showGrid.value,
    strokeWidth: strokeWidth.value,
    duration: animationDuration.value,
    smooth: smoothCurve.value,
    curveType: curveType.value as 'linear' | 'cardinal' | 'basis' | 'monotone',
    pathAnimation: pathAnimation.value,
    pathAnimationDuration: pathAnimationDuration.value,
    showAxisLabels: showAxisLabels.value,
    cursorFollowCurve: cursorFollowCurve.value,
    useWorker: useWorker.value,
    zoomLevel: enableZoom.value ? zoomLevel.value : 1,
    enableZoom: enableZoom.value,
    viewOffsetX: enableZoom.value ? viewOffsetX.value : 0,
    viewOffsetY: enableZoom.value ? viewOffsetY.value : 0
  })
}

// 重置图表
function resetChart() {
  if (chart.value) {
    chart.value.destroy()
  }
  initChart()
}

// 导出数据
function exportChartData() {
  if (!chart.value) return null
  
  return {
    data: props.data,
    options: chart.value.getOptions(),
    mousePosition: { x: mouseX.value, y: mouseY.value },
    chartCoordinates: getChartCoordinates()
  }
}

// 获取游标长度
function getCursorLength() {
  if (!chart.value) return 0

  const cursor = chart.value.getElement('main-cursor') as any
  if (!cursor || !cursor.getLength) return 0

  return Math.round(cursor.getLength())
}

// 处理缩放变化
function handleZoomChange() {
  if (!chart.value) return

  console.log('缩放级别改变:', zoomLevel.value, '偏移:', viewOffsetX.value, viewOffsetY.value)

  // 应用缩放到图表
  chart.value.updateOptions({
    zoomLevel: zoomLevel.value,
    enableZoom: enableZoom.value,
    viewOffsetX: viewOffsetX.value,
    viewOffsetY: viewOffsetY.value
  })
}

// 处理启用缩放变化
function handleEnableZoomChange() {
  if (!enableZoom.value) {
    zoomLevel.value = 1
    handleZoomChange()
  }
}

// 重置缩放
function resetZoom() {
  zoomLevel.value = 1
  viewOffsetX.value = 0
  viewOffsetY.value = 0
  isDraggingView.value = false
  handleZoomChange()
}

// 处理鼠标滚轮缩放
function handleWheel(event: WheelEvent) {
  // 防止滚动事件穿透到canvas外面
  event.preventDefault()

  if (!enableZoom.value) return

  // 获取鼠标在Canvas内的位置
  const rect = (event.target as HTMLElement).getBoundingClientRect()
  const mouseX = event.clientX - rect.left
  const mouseY = event.clientY - rect.top

  const delta = event.deltaY > 0 ? 0.9 : 1.1 // 向下滚动缩小，向上滚动放大
  const oldZoomLevel = zoomLevel.value
  const newZoomLevel = Math.max(0.5, Math.min(3, oldZoomLevel * delta))

  if (newZoomLevel !== oldZoomLevel) {
    // 计算缩放中心点并调整视图偏移
    const canvas = canvasBg.value || canvasFg.value
    if (canvas) {
      const centerX = canvas.width / 2
      const centerY = canvas.height / 2

      // 计算鼠标相对于画布中心的偏移
      const offsetX = mouseX - centerX
      const offsetY = mouseY - centerY

      // 缩放时调整视图偏移以保持鼠标位置为中心
      const zoomChange = oldZoomLevel / newZoomLevel
      viewOffsetX.value = offsetX - (offsetX * zoomChange)
      viewOffsetY.value = offsetY - (offsetY * zoomChange)
    }

    zoomLevel.value = newZoomLevel
    handleZoomChange()
  }
}

// 处理鼠标按下（开始拖拽）
function handleMouseDown(event: MouseEvent) {
  if (!enableZoom.value) return

  isDraggingView.value = true
  lastMouseX.value = event.clientX
  lastMouseY.value = event.clientY

  event.preventDefault()
}

// 处理鼠标移动（拖拽视图）
function handleMouseMove(event: MouseEvent) {
  if (!isDraggingView.value || !enableZoom.value) return

  const deltaX = event.clientX - lastMouseX.value
  const deltaY = event.clientY - lastMouseY.value

  viewOffsetX.value += deltaX
  viewOffsetY.value += deltaY

  lastMouseX.value = event.clientX
  lastMouseY.value = event.clientY

  handleZoomChange()

  event.preventDefault()
}

// 处理鼠标释放（停止拖拽）
function handleMouseUp() {
  isDraggingView.value = false
}

// 监听 props 变化
watch(() => props.data, () => {
  if (chart.value) {
    chart.value.updateData(props.data)
  }
}, { deep: true })

watch(() => props.options, () => {
  updateChartOptions()
}, { deep: true })

// 生命周期
onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (chart.value) {
    chart.value.destroy()
  }
})

// 暴露方法给父组件
defineExpose({
  chart,
  updateChartOptions,
  resetChart,
  exportChartData,
  isReady
})
</script>

<template>
  <div class="chart-wrapper">
    <!-- 控制面板 -->
    <div v-if="showControls" class="controls-panel">
      <h3>图表控制面板</h3>
      
      <div class="control-row">
        <label>颜色:</label>
        <input 
          v-model="currentColor" 
          type="color" 
          @change="updateChartOptions"
        >
      </div>
      
      <div class="control-row">
        <label>显示数据点:</label>
        <input 
          v-model="showPoints" 
          type="checkbox" 
          @change="updateChartOptions"
        >
      </div>
      
      <div class="control-row">
        <label>显示网格:</label>
        <input 
          v-model="showGrid" 
          type="checkbox" 
          @change="updateChartOptions"
        >
      </div>
      
      <div class="control-row">
        <label>显示坐标轴标签:</label>
        <input 
          v-model="showAxisLabels" 
          type="checkbox" 
          @change="updateChartOptions"
        >
      </div>
      
      <div class="control-row">
        <label>线条宽度:</label>
        <input 
          v-model.number="strokeWidth" 
          type="range" 
          min="1" 
          max="10" 
          @input="updateChartOptions"
        >
        <span>{{ strokeWidth }}px</span>
      </div>
      
      <div class="control-row">
        <label>动画时长:</label>
        <input 
          v-model.number="animationDuration" 
          type="range" 
          min="0.5" 
          max="5" 
          step="0.1" 
          @input="updateChartOptions"
        >
        <span>{{ animationDuration }}s</span>
      </div>
      
      <div class="control-row">
        <label>平滑曲线:</label>
        <input 
          v-model="smoothCurve" 
          type="checkbox" 
          @change="updateChartOptions"
        >
      </div>
      
      <div class="control-row" v-if="smoothCurve">
        <label>曲线类型:</label>
        <select 
          v-model="curveType" 
          @change="updateChartOptions"
        >
          <option value="cardinal">Cardinal (圆滑)</option>
          <option value="basis">Basis (B样条)</option>
          <option value="monotone">Monotone (单调)</option>
          <option value="linear">Linear (直线)</option>
        </select>
      </div>
      
      <div class="control-row">
        <label>路径动画:</label>
        <input 
          v-model="pathAnimation" 
          type="checkbox" 
          @change="updateChartOptions"
        >
      </div>

      <div class="control-row">
        <label>游标跟随曲线高度:</label>
        <input 
          v-model="cursorFollowCurve" 
          type="checkbox" 
          @change="updateChartOptions"
        >
      </div>

      <div class="control-row">
        <label>使用 Web Worker 加速:</label>
        <input
          v-model="useWorker"
          type="checkbox"
          @change="updateChartOptions"
        >
      </div>

      <div class="control-row">
        <label>启用缩放:</label>
        <input
          v-model="enableZoom"
          type="checkbox"
          @change="handleEnableZoomChange"
        >
      </div>

      <div class="control-row" v-if="enableZoom">
        <label>缩放级别:</label>
        <input
          v-model.number="zoomLevel"
          type="range"
          min="0.5"
          max="3"
          step="0.1"
          @input="handleZoomChange"
        >
        <span>{{ (zoomLevel * 100).toFixed(0) }}%</span>
      </div>

      <div class="control-row" v-if="enableZoom">
        <button @click="resetZoom">重置缩放</button>
      </div>

      <div class="control-row" v-if="pathAnimation">
        <label>路径动画时长:</label>
        <input
          v-model.number="pathAnimationDuration"
          type="range"
          min="0.5"
          max="5"
          step="0.1"
          @input="updateChartOptions"
        >
        <span>{{ pathAnimationDuration }}s</span>
      </div>
      
      <div class="control-row">
        <button @click="resetChart">重置图表</button>
        <button @click="showControls = false">隐藏控制面板</button>
      </div>
    </div>

    <!-- 图表容器 -->
    <div class="chart-container">
      <div class="canvas-wrapper" @wheel="handleWheel" @mousedown="handleMouseDown" @mousemove="handleMouseMove" @mouseup="handleMouseUp" @mouseleave="handleMouseUp">
        <canvas
          ref="canvasBg"
          class="chart-canvas chart-bg"
        ></canvas>
        <canvas
          ref="canvasFg"
          class="chart-canvas chart-fg"
        ></canvas>
      </div>
      
      <!-- 信息显示 -->
      <div class="chart-info">
        <div class="info-row">
          <span>鼠标位置: ({{ mouseX }}, {{ mouseY }})</span>
        </div>
        <div class="info-row">
          <span>图表坐标: {{ getChartCoordinates() }}</span>
        </div>
        <div class="info-row">
          <span>悬停状态: {{ isHovering ? '是' : '否' }}</span>
        </div>
        <div class="info-row">
          <span>拖拽状态: {{ isDragging ? '是' : '否' }}</span>
        </div>
        <div v-if="hoveredElement" class="info-row">
          <span>悬停元素: {{ hoveredElement }}</span>
        </div>
        <div v-if="chart" class="info-row">
          <span>游标长度: {{ getCursorLength() }}px</span>
        </div>
      </div>
    </div>

    <!-- 显示控制面板按钮 -->
    <button 
      v-if="!showControls" 
      class="show-controls-btn"
      @click="showControls = true"
    >
      显示控制面板
    </button>
  </div>
</template>

<style scoped>
.chart-wrapper {
  display: flex;
  flex-direction: row;
  gap: 20px;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.controls-panel {
  background: #f5f5f5;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.controls-panel h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.control-row {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.control-row label {
  min-width: 100px;
  font-weight: 500;
}

.control-row input[type="range"] {
  flex: 1;
  max-width: 200px;
}

.control-row button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background: #4285f4;
  color: white;
  cursor: pointer;
  transition: background 0.2s;
}

.control-row button:hover {
  background: #3367d6;
}

.chart-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.canvas-wrapper {
  position: relative;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-canvas {
  display: block;
}

.chart-bg {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.chart-fg {
  position: relative;
  z-index: 2;
}

.chart-info {
  background: #f9f9f9;
  border-radius: 6px;
  padding: 15px;
  font-size: 14px;
}

.info-row {
  margin-bottom: 5px;
  color: #666;
}

.info-row:last-child {
  margin-bottom: 0;
}

.show-controls-btn {
  align-self: flex-start;
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  background: #4285f4;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.show-controls-btn:hover {
  background: #3367d6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-wrapper {
    padding: 10px;
  }
  
  .controls-panel {
    padding: 15px;
  }
  
  .control-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .control-row label {
    min-width: auto;
  }
}
</style>