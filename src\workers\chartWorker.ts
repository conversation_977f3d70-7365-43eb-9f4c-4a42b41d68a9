/* chartWorker.ts: 负责计算插值、缩放变换、路径生成等 CPU 密集任务 */

export type Point = { x: number; y: number; currentY?: number };

export type WorkerRequest =
  | InterpolateRequest
  | ScaleCalculationRequest
  | PathGenerationRequest
  | DataProcessingRequest;

export type InterpolateRequest = {
  id: number;
  type: 'interpolateAtX';
  data: {
    points: Point[];
    targetX: number;
  };
};

export type ScaleCalculationRequest = {
  id: number;
  type: 'calculateScales';
  data: {
    points: Point[];
    width: number;
    height: number;
    margin: { top: number; right: number; bottom: number; left: number };
    zoomLevel: number;
    viewOffsetX: number;
    viewOffsetY: number;
  };
};

export type PathGenerationRequest = {
  id: number;
  type: 'generatePath';
  data: {
    points: Point[];
    curveType: 'linear' | 'cardinal' | 'basis' | 'monotone';
    progress: number;
  };
};

export type DataProcessingRequest = {
  id: number;
  type: 'processData';
  data: {
    points: Point[];
    operations: ('smooth' | 'downsample' | 'normalize')[];
  };
};

// 插值计算
function interpolateAtX(points: Point[], x: number): number {
  if (!points.length) return 0;
  if (x <= points[0].x) return points[0].currentY ?? points[0].y;
  if (x >= points[points.length - 1].x) {
    const last = points[points.length - 1];
    return last.currentY ?? last.y;
  }
  for (let i = 0; i < points.length - 1; i++) {
    const a = points[i];
    const b = points[i + 1];
    if (a.x <= x && x <= b.x) {
      const ay = a.currentY ?? a.y;
      const by = b.currentY ?? b.y;
      const t = (x - a.x) / (b.x - a.x || 1);
      return ay + (by - ay) * t;
    }
  }
  const last = points[points.length - 1];
  return last.currentY ?? last.y;
}

// 缩放比例尺计算
function calculateScales(data: ScaleCalculationRequest['data']) {
  const { points, width, height, margin, zoomLevel, viewOffsetX, viewOffsetY } = data;

  const xMax = Math.max(...points.map(p => p.x));
  const yMax = Math.max(...points.map(p => p.y));

  if (Math.abs(zoomLevel - 1) < 0.001) {
    return {
      xDomain: [0, xMax],
      yDomain: [0, yMax],
      xRange: [margin.left, margin.left + width],
      yRange: [margin.top + height, margin.top]
    };
  }

  const zoomFactor = 1 / zoomLevel;
  const visibleXRange = xMax * zoomFactor;
  const visibleYRange = yMax * zoomFactor;

  const offsetXData = (viewOffsetX / width) * visibleXRange;
  const offsetYData = -(viewOffsetY / height) * visibleYRange;

  const centerX = xMax / 2 + offsetXData;
  const centerY = yMax / 2 + offsetYData;

  const halfVisibleX = visibleXRange / 2;
  const halfVisibleY = visibleYRange / 2;

  let xMin = centerX - halfVisibleX;
  let xMaxVisible = centerX + halfVisibleX;
  let yMin = centerY - halfVisibleY;
  let yMaxVisible = centerY + halfVisibleY;

  // 边界检查
  if (xMin < 0) {
    xMaxVisible += -xMin;
    xMin = 0;
  }
  if (xMaxVisible > xMax) {
    xMin -= (xMaxVisible - xMax);
    xMaxVisible = xMax;
    xMin = Math.max(0, xMin);
  }

  if (yMin < 0) {
    yMaxVisible += -yMin;
    yMin = 0;
  }
  if (yMaxVisible > yMax) {
    yMin -= (yMaxVisible - yMax);
    yMaxVisible = yMax;
    yMin = Math.max(0, yMin);
  }

  return {
    xDomain: [xMin, xMaxVisible],
    yDomain: [yMin, yMaxVisible],
    xRange: [margin.left, margin.left + width],
    yRange: [margin.top + height, margin.top]
  };
}

// 路径生成（简化版，主要用于预计算）
function generatePath(data: PathGenerationRequest['data']) {
  const { points, progress } = data;

  if (points.length < 2) return [];

  const n = points.length;
  const p = Math.max(0, Math.min(1, progress));
  const t = p * (n - 1);
  const k = Math.floor(t);
  const f = t - k;

  const result = points.slice(0, k + 1);

  if (k < n - 1) {
    const a = points[k];
    const b = points[k + 1];
    const ax = a.x, bx = b.x;
    const ay = a.currentY ?? a.y;
    const by = b.currentY ?? b.y;
    const ix = ax + (bx - ax) * f;
    const iy = ay + (by - ay) * f;
    result.push({ x: ix, y: iy, currentY: iy });
  }

  return result;
}

// 数据处理
function processData(data: DataProcessingRequest['data']) {
  const { points, operations } = data;
  let result = [...points];

  for (const op of operations) {
    switch (op) {
      case 'smooth':
        // 简单的移动平均平滑
        if (result.length > 2) {
          const smoothed = [result[0]];
          for (let i = 1; i < result.length - 1; i++) {
            const prev = result[i - 1];
            const curr = result[i];
            const next = result[i + 1];
            smoothed.push({
              x: curr.x,
              y: (prev.y + curr.y + next.y) / 3,
              currentY: curr.currentY
            });
          }
          smoothed.push(result[result.length - 1]);
          result = smoothed;
        }
        break;
      case 'downsample':
        // 简单的降采样
        if (result.length > 100) {
          const step = Math.ceil(result.length / 100);
          result = result.filter((_, i) => i % step === 0);
        }
        break;
      case 'normalize':
        // 数据归一化
        const maxY = Math.max(...result.map(p => p.y));
        if (maxY > 0) {
          result = result.map(p => ({
            ...p,
            y: p.y / maxY
          }));
        }
        break;
    }
  }

  return result;
}

self.onmessage = (ev: MessageEvent) => {
  const msg = ev.data as WorkerRequest;
  if (!msg || !msg.id) return;

  let result: any;

  try {
    switch (msg.type) {
      case 'interpolateAtX':
        result = interpolateAtX(msg.data.points, msg.data.targetX);
        break;
      case 'calculateScales':
        result = calculateScales(msg.data);
        break;
      case 'generatePath':
        result = generatePath(msg.data);
        break;
      case 'processData':
        result = processData(msg.data);
        break;
      default:
        throw new Error(`Unknown message type: ${(msg as any).type}`);
    }

    (self as any).postMessage({ id: msg.id, result, success: true });
  } catch (error) {
    (self as any).postMessage({
      id: msg.id,
      error: error instanceof Error ? error.message : 'Unknown error',
      success: false
    });
  }
};