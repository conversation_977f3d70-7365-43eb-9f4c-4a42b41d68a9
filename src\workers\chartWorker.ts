/* chartWorker.ts: 负责计算插值等 CPU 密集任务 */
export type InterpolateRequest = {
  id: number;
  type: 'interpolateAtX';
  data: {
    points: { x: number; y: number; currentY?: number }[];
    targetX: number;
  };
};

function interpolateAtX(points: { x: number; y: number; currentY?: number }[], x: number): number {
  if (!points.length) return 0;
  if (x <= points[0].x) return points[0].currentY ?? points[0].y;
  if (x >= points[points.length - 1].x) {
    const last = points[points.length - 1];
    return last.currentY ?? last.y;
  }
  for (let i = 0; i < points.length - 1; i++) {
    const a = points[i];
    const b = points[i + 1];
    if (a.x <= x && x <= b.x) {
      const ay = a.currentY ?? a.y;
      const by = b.currentY ?? b.y;
      const t = (x - a.x) / (b.x - a.x || 1);
      return ay + (by - ay) * t;
    }
  }
  const last = points[points.length - 1];
  return last.currentY ?? last.y;
}

self.onmessage = (ev: MessageEvent) => {
  const msg = ev.data as InterpolateRequest;
  if (!msg || msg.type !== 'interpolateAtX') return;
  const result = interpolateAtX(msg.data.points, msg.data.targetX);
  (self as any).postMessage({ id: msg.id, result });
};