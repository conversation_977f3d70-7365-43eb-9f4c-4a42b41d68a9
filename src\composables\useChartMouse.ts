import { ref, onMounted, onUnmounted, type Ref } from "vue";

// 定义通用的交互元素接口，避免直接依赖 chartEngine
export interface InteractiveElement {
    id: string;
    isPointInside(x: number, y: number): boolean;
}

// 定义图表接口，只包含必要的方法
export interface ChartLike {
    getElement(id: string): InteractiveElement | undefined;
    getAllElements(): { [key: string]: InteractiveElement };
    getDataCoordinates?(screenX: number, screenY: number): { x: number; y: number };
}

/**
 * 通用的 Canvas 鼠标交互组合式函数
 * 不依赖具体的图表实现，通过接口进行解耦
 */
export function useCanvasMouse(
    canvasRef: Ref<HTMLCanvasElement | null>,
    interactiveObject?: Ref<ChartLike | null>
) {
    const mouseX = ref(0);
    const mouseY = ref(0);
    const isHovering = ref(false);
    const isDragging = ref(false);
    const hoveredElement = ref<string | null>(null);

    // 鼠标移动处理
    function handleMouseMove(event: MouseEvent) {
        if (!canvasRef.value) return;

        // 更新鼠标坐标
        const rect = canvasRef.value.getBoundingClientRect();
        mouseX.value = event.clientX - rect.left;
        mouseY.value = event.clientY - rect.top;

        // 如果有交互对象，检查悬停状态
        if (interactiveObject?.value) {
            const hitElement = findHitElement(mouseX.value, mouseY.value);
            
            if (hitElement) {
                isHovering.value = true;
                hoveredElement.value = hitElement.id;
                canvasRef.value.style.cursor = 'pointer';
            } else {
                isHovering.value = false;
                hoveredElement.value = null;
                canvasRef.value.style.cursor = isDragging.value ? 'grabbing' : 'default';
            }
        }
    }

    // 鼠标按下处理
    function handleMouseDown(event: MouseEvent) {
        if (!canvasRef.value) return;

        const rect = canvasRef.value.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        if (interactiveObject?.value) {
            const hitElement = findHitElement(x, y);
            if (hitElement) {
                isDragging.value = true;
                canvasRef.value.style.cursor = 'grabbing';
            }
        }
    }

    // 鼠标释放处理
    function handleMouseUp() {
        if (isDragging.value) {
            isDragging.value = false;
            if (canvasRef.value) {
                canvasRef.value.style.cursor = isHovering.value ? 'pointer' : 'default';
            }
        }
    }

    // 查找被点击的元素（通过接口调用，不直接访问内部结构）
    function findHitElement(x: number, y: number): InteractiveElement | null {
        if (!interactiveObject?.value) return null;

        const elements = interactiveObject.value.getAllElements();
        for (const id in elements) {
            const element = elements[id];
            if (element.isPointInside(x, y)) {
                return element;
            }
        }
        return null;
    }

    // 获取数据坐标（如果图表支持的话）
    function getDataCoordinates() {
        if (!interactiveObject?.value?.getDataCoordinates) {
            return { x: mouseX.value, y: mouseY.value };
        }

        try {
            return interactiveObject.value.getDataCoordinates(mouseX.value, mouseY.value);
        } catch (error) {
            return { x: mouseX.value, y: mouseY.value };
        }
    }

    // 添加自定义鼠标事件监听器
    function onMouseEvent(eventType: 'move' | 'down' | 'up', callback: (event: MouseEvent) => void) {
        if (!canvasRef.value) return;

        const eventMap = {
            move: 'mousemove' as const,
            down: 'mousedown' as const,
            up: 'mouseup' as const
        };

        const wrappedCallback = (event: Event) => {
            callback(event as MouseEvent);
        };

        canvasRef.value.addEventListener(eventMap[eventType], wrappedCallback);

        // 返回清理函数
        return () => {
            canvasRef.value?.removeEventListener(eventMap[eventType], wrappedCallback);
        };
    }

    // 生命周期管理
    onMounted(() => {
        if (!canvasRef.value) return;

        canvasRef.value.addEventListener('mousemove', handleMouseMove);
        canvasRef.value.addEventListener('mousedown', handleMouseDown);
        document.addEventListener('mouseup', handleMouseUp);
    });

    onUnmounted(() => {
        if (!canvasRef.value) return;

        canvasRef.value.removeEventListener('mousemove', handleMouseMove);
        canvasRef.value.removeEventListener('mousedown', handleMouseDown);
        document.removeEventListener('mouseup', handleMouseUp);
    });

    return {
        // 响应式状态
        mouseX,
        mouseY,
        isHovering,
        isDragging,
        hoveredElement,
        
        // 方法
        getDataCoordinates,
        onMouseEvent,
        findHitElement
    };
}

/**
 * 图表专用的鼠标交互函数（向后兼容）
 */
export function useChartMouse(
    canvasRef: Ref<HTMLCanvasElement | null>,
    chart: Ref<any>
) {
    return useCanvasMouse(canvasRef, chart);
}

/**
 * 简化版本的图表鼠标追踪
 * 只提供基础的坐标追踪功能
 */
export function useChartMouseSimple(canvasRef: Ref<HTMLCanvasElement | null>) {
    const x = ref(0);
    const y = ref(0);

    function update(event: MouseEvent) {
        if (canvasRef.value) {
            const rect = canvasRef.value.getBoundingClientRect();
            x.value = event.clientX - rect.left;
            y.value = event.clientY - rect.top;
        }
    }

    onMounted(() => {
        canvasRef.value?.addEventListener('mousemove', update);
    });

    onUnmounted(() => {
        canvasRef.value?.removeEventListener('mousemove', update);
    });

    return { x, y };
}