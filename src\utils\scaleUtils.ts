/**
 * 刻度计算工具
 * 提供标准的数学刻度计算和自定义刻度间距
 */

export interface TickConfig {
  min: number
  max: number
  targetCount?: number
  customStep?: number
  forceStep?: boolean
}

export interface TickResult {
  ticks: number[]
  step: number
  min: number
  max: number
}

/**
 * 计算合适的刻度步长
 * 返回标准的数学刻度（1, 2, 5 的倍数）
 */
export function calculateNiceStep(range: number, targetCount: number = 10): number {
  const roughStep = range / targetCount
  const magnitude = Math.pow(10, Math.floor(Math.log10(roughStep)))
  
  // 标准的刻度倍数：1, 2, 5
  const normalizedStep = roughStep / magnitude
  let niceStep: number
  
  if (normalizedStep <= 1) {
    niceStep = 1
  } else if (normalizedStep <= 2) {
    niceStep = 2
  } else if (normalizedStep <= 5) {
    niceStep = 5
  } else {
    niceStep = 10
  }
  
  return niceStep * magnitude
}

/**
 * 计算合适的刻度范围
 * 确保刻度从整数开始和结束
 */
export function calculateNiceRange(min: number, max: number, step: number): { min: number, max: number } {
  const niceMin = Math.floor(min / step) * step
  const niceMax = Math.ceil(max / step) * step
  
  return { min: niceMin, max: niceMax }
}

/**
 * 生成刻度数组
 */
export function generateTicks(config: TickConfig): TickResult {
  const { min, max, targetCount = 10, customStep, forceStep = false } = config
  
  let step: number
  let tickMin: number
  let tickMax: number
  
  if (customStep && forceStep) {
    // 强制使用自定义步长
    step = customStep
    tickMin = Math.floor(min / step) * step
    tickMax = Math.ceil(max / step) * step
  } else if (customStep) {
    // 使用自定义步长，但允许调整范围
    step = customStep
    const range = calculateNiceRange(min, max, step)
    tickMin = range.min
    tickMax = range.max
  } else {
    // 自动计算最佳步长
    const range = max - min
    step = calculateNiceStep(range, targetCount)
    const niceRange = calculateNiceRange(min, max, step)
    tickMin = niceRange.min
    tickMax = niceRange.max
  }
  
  // 生成刻度数组
  const ticks: number[] = []
  let current = tickMin
  
  // 防止无限循环
  const maxTicks = 1000
  let count = 0
  
  while (current <= tickMax && count < maxTicks) {
    // 处理浮点数精度问题
    const roundedCurrent = Math.round(current / step) * step
    ticks.push(parseFloat(roundedCurrent.toFixed(10)))
    current += step
    count++
  }
  
  return {
    ticks,
    step,
    min: tickMin,
    max: tickMax
  }
}

/**
 * 格式化刻度标签
 * 自动处理小数位数
 */
export function formatTickLabel(value: number, step: number): string {
  // 根据步长确定小数位数
  const decimalPlaces = Math.max(0, -Math.floor(Math.log10(step)))
  
  if (decimalPlaces === 0) {
    return value.toString()
  } else {
    return value.toFixed(decimalPlaces)
  }
}

/**
 * 预定义的常用刻度步长
 */
export const COMMON_STEPS = {
  // 整数步长
  integers: [1, 2, 5, 10, 20, 50, 100, 200, 500, 1000],
  
  // 小数步长
  decimals: [0.01, 0.02, 0.05, 0.1, 0.2, 0.5],
  
  // 百分比步长
  percentages: [0.01, 0.05, 0.1, 0.25, 0.5, 1, 2, 5, 10, 25, 50],
  
  // 时间步长（秒）
  time: [1, 5, 10, 15, 30, 60, 300, 600, 1800, 3600]
}

/**
 * 根据数据范围推荐合适的步长
 */
export function recommendStep(min: number, max: number, type: 'auto' | 'integer' | 'decimal' | 'percentage' = 'auto'): number {
  const range = max - min
  
  let candidates: number[]
  
  switch (type) {
    case 'integer':
      candidates = COMMON_STEPS.integers
      break
    case 'decimal':
      candidates = COMMON_STEPS.decimals
      break
    case 'percentage':
      candidates = COMMON_STEPS.percentages
      break
    default:
      candidates = [
        ...COMMON_STEPS.decimals,
        ...COMMON_STEPS.integers
      ].sort((a, b) => a - b)
  }
  
  // 找到最接近理想步长的候选值
  const idealStep = calculateNiceStep(range)
  
  let bestStep = candidates[0]
  let minDiff = Math.abs(idealStep - bestStep)
  
  for (const step of candidates) {
    const diff = Math.abs(idealStep - step)
    if (diff < minDiff) {
      minDiff = diff
      bestStep = step
    }
  }
  
  return bestStep
}

/**
 * 缩放步进计算
 */
export interface ZoomConfig {
  currentZoom: number
  step: number // 百分比，如 1 表示 1%
  min: number
  max: number
}

/**
 * 计算下一个缩放级别
 */
export function calculateNextZoom(config: ZoomConfig, direction: 'in' | 'out'): number {
  const { currentZoom, step, min, max } = config
  
  // 将百分比转换为倍数
  const stepMultiplier = step / 100
  
  let nextZoom: number
  
  if (direction === 'in') {
    // 放大：增加百分比
    nextZoom = currentZoom * (1 + stepMultiplier)
  } else {
    // 缩小：减少百分比
    nextZoom = currentZoom * (1 - stepMultiplier)
  }
  
  // 应用边界限制
  return Math.max(min, Math.min(max, nextZoom))
}

/**
 * 生成缩放级别数组
 */
export function generateZoomLevels(min: number = 0.1, max: number = 5, step: number = 1): number[] {
  const levels: number[] = []
  let current = min
  
  while (current <= max) {
    levels.push(parseFloat(current.toFixed(3)))
    current = calculateNextZoom({ currentZoom: current, step, min, max }, 'in')
    
    // 防止无限循环
    if (levels.length > 1000) break
  }
  
  return levels
}
