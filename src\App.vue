<script setup lang="ts">
import Charts from './components/charts.vue'

// 示例数据
const sampleData = [
  { x: 0, y: 10 },
  { x: 1, y: 25 },
  { x: 2, y: 15 },
  { x: 3, y: 40 },
  { x: 4, y: 30 },
  { x: 5, y: 50 },
  { x: 6, y: 35 },
  { x: 7, y: 60 },
  { x: 8, y: 45 },
  { x: 9, y: 70 }
]

// 事件处理
function handleCursorMove(x: number) {
  console.log('游标移动到:', x)
}

function handleAnimationComplete() {
  console.log('动画完成')
}

function handleChartReady(chart: any) {
  console.log('图表就绪:', chart)
}
</script>

<template>
  <div id="app">
    <div class="chart-section">
      <Charts 
        :data="sampleData"
        :width="900"
        :height="500"
        :options="{
          color: '#4285f4',
          showPoints: true,
          strokeWidth: 3,
          duration: 2,
          showGrid: true,
          showAxisLabels: true,
          smooth: true,
          curveType: 'cardinal',
          pathAnimation: true,
          pathAnimationDuration: 2.5
        }"
        @cursor-move="handleCursorMove"
        @data-animation-complete="handleAnimationComplete"
        @chart-ready="handleChartReady"
      />
    </div>
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  width: 100%;
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: white;
  color: #333;
}

.app-header {
  text-align: center;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  margin-bottom: 30px;
}

.app-header h1 {
  font-size: 2.5rem;
  margin-bottom: 15px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.app-header p {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.chart-section {
  display: flex;
  justify-content: center;
  padding: 0 20px;
  margin-bottom: 50px;
}

.features-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 50px 20px;
  margin-top: 50px;
}

.features-section h2 {
  text-align: center;
  font-size: 2rem;
  margin-bottom: 40px;
  color: #333;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  max-width: 1000px;
  margin: 0 auto;
}

.feature-card {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.feature-card h3 {
  font-size: 1.3rem;
  margin-bottom: 15px;
  color: #333;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-header h1 {
    font-size: 2rem;
  }
  
  .app-header p {
    font-size: 1rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .feature-card {
    padding: 20px;
  }
}
</style>
